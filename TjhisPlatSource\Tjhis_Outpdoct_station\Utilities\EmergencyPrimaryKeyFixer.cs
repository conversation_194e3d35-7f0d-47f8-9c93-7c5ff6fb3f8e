using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PlatCommon.SysBase;
using Tjhis.Outpdoct.Station.Comm;

namespace Tjhis.Outpdoct.Station.Utilities
{
    /// <summary>
    /// 紧急主键冲突修复工具
    /// 当发生ORA-00001错误时，提供紧急修复方案
    /// </summary>
    public class EmergencyPrimaryKeyFixer
    {
        private readonly OracleDataHelper db;
        private static readonly string LogDirectory = Path.Combine(Application.StartupPath, "LOG", "exLOG");
        private static readonly object lockObj = new object();

        public EmergencyPrimaryKeyFixer()
        {
            db = new OracleDataHelper();
            
            // 确保日志目录存在
            if (!Directory.Exists(LogDirectory))
            {
                Directory.CreateDirectory(LogDirectory);
            }
        }

        /// <summary>
        /// 紧急修复主键冲突
        /// </summary>
        /// <param name="clinicNo">诊疗号</param>
        /// <param name="conflictOrderNo">冲突的ORDER_NO</param>
        /// <param name="conflictOrderSubNo">冲突的ORDER_SUB_NO</param>
        /// <returns>修复结果</returns>
        public FixResult EmergencyFix(string clinicNo, int conflictOrderNo, int conflictOrderSubNo)
        {
            var result = new FixResult
            {
                ClinicNo = clinicNo,
                ConflictOrderNo = conflictOrderNo,
                ConflictOrderSubNo = conflictOrderSubNo,
                FixTime = DateTime.Now,
                Success = false,
                Messages = new List<string>()
            };

            try
            {
                WriteLog($"开始紧急修复 - 诊疗号: {clinicNo}, ORDER_NO: {conflictOrderNo}, ORDER_SUB_NO: {conflictOrderSubNo}");

                // 1. 确认冲突确实存在
                if (!ConfirmConflictExists(clinicNo, conflictOrderNo, conflictOrderSubNo))
                {
                    result.Messages.Add("未发现指定的主键冲突，可能已经被解决");
                    result.Success = true;
                    return result;
                }

                // 2. 获取新的ORDER_NO
                int newOrderNo = GetNextAvailableOrderNo(clinicNo);
                if (newOrderNo <= 0)
                {
                    result.Messages.Add("无法获取新的ORDER_NO");
                    return result;
                }

                // 3. 执行修复
                if (ExecuteFix(clinicNo, conflictOrderNo, conflictOrderSubNo, newOrderNo))
                {
                    result.Success = true;
                    result.NewOrderNo = newOrderNo;
                    result.Messages.Add($"修复成功：ORDER_NO从 {conflictOrderNo} 更改为 {newOrderNo}");
                    WriteLog($"修复成功 - 新ORDER_NO: {newOrderNo}");
                }
                else
                {
                    result.Messages.Add("修复执行失败");
                }
            }
            catch (Exception ex)
            {
                result.Messages.Add($"修复过程异常: {ex.Message}");
                WriteLog($"修复异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 确认冲突是否存在
        /// </summary>
        private bool ConfirmConflictExists(string clinicNo, int orderNo, int orderSubNo)
        {
            try
            {
                string sql = @"SELECT COUNT(*) FROM OUTPDOCT.OUTP_ORDERS_STANDARD 
                              WHERE CLINIC_NO = :clinic_no 
                              AND ORDER_NO = :order_no 
                              AND ORDER_SUB_NO = :order_sub_no";

                List<DbParameter> parameters = new List<DbParameter>
                {
                    db.CreateDbParameter(":clinic_no", clinicNo),
                    db.CreateDbParameter(":order_no", orderNo),
                    db.CreateDbParameter(":order_sub_no", orderSubNo)
                };

                object result = db.GetSingleValue(sql, parameters);
                int count = Convert.ToInt32(result ?? 0);
                
                WriteLog($"冲突确认 - 记录数: {count}");
                return count > 1; // 大于1表示有重复
            }
            catch (Exception ex)
            {
                WriteLog($"确认冲突异常: {ex.Message}");
                return true; // 异常时保守处理
            }
        }

        /// <summary>
        /// 获取下一个可用的ORDER_NO
        /// </summary>
        private int GetNextAvailableOrderNo(string clinicNo)
        {
            try
            {
                // 获取当前最大的ORDER_NO
                string sql = @"SELECT NVL(MAX(ORDER_NO), 0) FROM OUTPDOCT.OUTP_ORDERS_STANDARD 
                              WHERE CLINIC_NO = :clinic_no";

                List<DbParameter> parameters = new List<DbParameter>
                {
                    db.CreateDbParameter(":clinic_no", clinicNo)
                };

                object result = db.GetSingleValue(sql, parameters);
                int maxOrderNo = Convert.ToInt32(result ?? 0);
                
                // 从最大值+1开始寻找可用的ORDER_NO
                int candidateOrderNo = maxOrderNo + 1;
                
                // 确保候选ORDER_NO不会冲突
                while (CheckOrderNoExists(clinicNo, candidateOrderNo))
                {
                    candidateOrderNo++;
                    
                    // 防止无限循环
                    if (candidateOrderNo > maxOrderNo + 1000)
                    {
                        WriteLog("寻找可用ORDER_NO超出合理范围");
                        return -1;
                    }
                }

                WriteLog($"找到可用ORDER_NO: {candidateOrderNo}");
                return candidateOrderNo;
            }
            catch (Exception ex)
            {
                WriteLog($"获取可用ORDER_NO异常: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// 检查ORDER_NO是否已存在
        /// </summary>
        private bool CheckOrderNoExists(string clinicNo, int orderNo)
        {
            try
            {
                string sql = @"SELECT COUNT(*) FROM OUTPDOCT.OUTP_ORDERS_STANDARD 
                              WHERE CLINIC_NO = :clinic_no AND ORDER_NO = :order_no";

                List<DbParameter> parameters = new List<DbParameter>
                {
                    db.CreateDbParameter(":clinic_no", clinicNo),
                    db.CreateDbParameter(":order_no", orderNo)
                };

                object result = db.GetSingleValue(sql, parameters);
                return Convert.ToInt32(result ?? 0) > 0;
            }
            catch
            {
                return true; // 异常时保守处理
            }
        }

        /// <summary>
        /// 执行修复操作
        /// </summary>
        private bool ExecuteFix(string clinicNo, int oldOrderNo, int orderSubNo, int newOrderNo)
        {
            try
            {
                // 开始事务
                db.BeginTransaction();

                try
                {
                    // 1. 更新OUTP_ORDERS_STANDARD表中最新的一条记录
                    string updateOrderSql = @"UPDATE OUTPDOCT.OUTP_ORDERS_STANDARD 
                                             SET ORDER_NO = :new_order_no 
                                             WHERE CLINIC_NO = :clinic_no 
                                             AND ORDER_NO = :old_order_no 
                                             AND ORDER_SUB_NO = :order_sub_no 
                                             AND ROWID = (
                                                 SELECT MAX(ROWID) FROM OUTPDOCT.OUTP_ORDERS_STANDARD 
                                                 WHERE CLINIC_NO = :clinic_no 
                                                 AND ORDER_NO = :old_order_no 
                                                 AND ORDER_SUB_NO = :order_sub_no
                                             )";

                    List<DbParameter> orderParams = new List<DbParameter>
                    {
                        db.CreateDbParameter(":new_order_no", newOrderNo),
                        db.CreateDbParameter(":clinic_no", clinicNo),
                        db.CreateDbParameter(":old_order_no", oldOrderNo),
                        db.CreateDbParameter(":order_sub_no", orderSubNo)
                    };

                    int orderRowsAffected = db.ExecuteNonQuery(updateOrderSql, orderParams);
                    WriteLog($"更新OUTP_ORDERS_STANDARD表，影响行数: {orderRowsAffected}");

                    // 2. 更新相关的费用表
                    string updateCostsSql = @"UPDATE OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD 
                                             SET ORDER_NO = :new_order_no 
                                             WHERE CLINIC_NO = :clinic_no 
                                             AND ORDER_NO = :old_order_no 
                                             AND ORDER_SUB_NO = :order_sub_no";

                    List<DbParameter> costsParams = new List<DbParameter>
                    {
                        db.CreateDbParameter(":new_order_no", newOrderNo),
                        db.CreateDbParameter(":clinic_no", clinicNo),
                        db.CreateDbParameter(":old_order_no", oldOrderNo),
                        db.CreateDbParameter(":order_sub_no", orderSubNo)
                    };

                    int costsRowsAffected = db.ExecuteNonQuery(updateCostsSql, costsParams);
                    WriteLog($"更新OUTP_ORDERS_COSTS_STANDARD表，影响行数: {costsRowsAffected}");

                    // 提交事务
                    db.CommitTransaction();
                    
                    WriteLog($"修复完成 - 总影响行数: {orderRowsAffected + costsRowsAffected}");
                    return true;
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    db.RollbackTransaction();
                    WriteLog($"修复执行失败，已回滚: {ex.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                WriteLog($"修复事务异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 批量修复重复记录
        /// </summary>
        /// <param name="clinicNo">诊疗号</param>
        /// <returns>批量修复结果</returns>
        public BatchFixResult BatchFix(string clinicNo)
        {
            var result = new BatchFixResult
            {
                ClinicNo = clinicNo,
                FixTime = DateTime.Now,
                FixedCount = 0,
                FailedCount = 0,
                Messages = new List<string>()
            };

            try
            {
                WriteLog($"开始批量修复 - 诊疗号: {clinicNo}");

                // 查找所有重复记录
                string sql = @"SELECT CLINIC_NO, ORDER_NO, ORDER_SUB_NO, COUNT(*) as DUPLICATE_COUNT
                              FROM OUTPDOCT.OUTP_ORDERS_STANDARD 
                              WHERE CLINIC_NO = :clinic_no
                              GROUP BY CLINIC_NO, ORDER_NO, ORDER_SUB_NO
                              HAVING COUNT(*) > 1";

                List<DbParameter> parameters = new List<DbParameter>
                {
                    db.CreateDbParameter(":clinic_no", clinicNo)
                };

                DataTable dt = db.QueryList(sql, parameters);
                
                if (dt.Rows.Count == 0)
                {
                    result.Messages.Add("未发现重复记录");
                    WriteLog("未发现重复记录");
                    return result;
                }

                WriteLog($"发现 {dt.Rows.Count} 组重复记录");

                // 逐个修复
                foreach (DataRow row in dt.Rows)
                {
                    int orderNo = Convert.ToInt32(row["ORDER_NO"]);
                    int orderSubNo = Convert.ToInt32(row["ORDER_SUB_NO"]);
                    int duplicateCount = Convert.ToInt32(row["DUPLICATE_COUNT"]);

                    var fixResult = EmergencyFix(clinicNo, orderNo, orderSubNo);
                    if (fixResult.Success)
                    {
                        result.FixedCount++;
                        result.Messages.Add($"修复成功: ORDER_NO {orderNo} -> {fixResult.NewOrderNo}");
                    }
                    else
                    {
                        result.FailedCount++;
                        result.Messages.Add($"修复失败: ORDER_NO {orderNo}, 原因: {string.Join(", ", fixResult.Messages)}");
                    }
                }

                WriteLog($"批量修复完成 - 成功: {result.FixedCount}, 失败: {result.FailedCount}");
            }
            catch (Exception ex)
            {
                result.Messages.Add($"批量修复异常: {ex.Message}");
                WriteLog($"批量修复异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 写入修复日志
        /// </summary>
        private void WriteLog(string message)
        {
            try
            {
                lock (lockObj)
                {
                    string logFile = Path.Combine(LogDirectory, $"EmergencyFixer_{DateTime.Now:yyyyMMdd}.log");
                    string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}{Environment.NewLine}";
                    File.AppendAllText(logFile, logEntry, Encoding.UTF8);
                }
            }
            catch
            {
                // 忽略日志写入错误
            }
        }
    }

    /// <summary>
    /// 修复结果
    /// </summary>
    public class FixResult
    {
        public string ClinicNo { get; set; }
        public int ConflictOrderNo { get; set; }
        public int ConflictOrderSubNo { get; set; }
        public int NewOrderNo { get; set; }
        public DateTime FixTime { get; set; }
        public bool Success { get; set; }
        public List<string> Messages { get; set; }
    }

    /// <summary>
    /// 批量修复结果
    /// </summary>
    public class BatchFixResult
    {
        public string ClinicNo { get; set; }
        public DateTime FixTime { get; set; }
        public int FixedCount { get; set; }
        public int FailedCount { get; set; }
        public List<string> Messages { get; set; }
    }
}
