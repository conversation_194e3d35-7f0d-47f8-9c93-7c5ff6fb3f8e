﻿using DevExpress.XtraEditors;
using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Tjhis.EmrOutp.Write.Comm.Globals;
using Tjhis.Interface.Station.RA.YiTu;
using Tjhis.Outpdoct.Station.Common;
using Tjhis.Outpdoct.Station.Dal;
using Tjhis.Outpdoct.Station.Interface;
using Tjhis.Outpdoct.Station.Interface.api;
using Tjhis.Outpdoct.Station.Maintain;
using Tjhis.Outpdoct.Station.Model;

using Tjhis.Outpdoct.Station.Views;
using Tjhis.Outpdoct.Station.Views.Blood.CommonSrv;
using Tjhis.Outpdoct.Station.Views.Presc;
using Tjhis.Outpdoct.Station.Views.Surg.CommonSrv;

namespace Tjhis.Outpdoct.Station.Business
{
    public class OrderBusiness : IOrders
    {
        private int _maxOrderNo;
        public int MaxOrderNo
        {
            get { return _maxOrderNo; }
            set
            {
                if (_maxOrderNo != value)
                {
                    try
                    {
                        LogMaxOrderNoChange("OrderBusiness.MaxOrderNo.set", _maxOrderNo, value, "属性直接设置");
                    }
                    catch
                    {
                        // 忽略日志错误，避免影响主业务
                    }
                    _maxOrderNo = value;
                }
            }
        }

        public class MyEventArgs : EventArgs
        {
            public OutpPatientInfo pi;
            public List<OUTP_ORDERS_STANDARD> addOrders;
        }

        public OrderBusiness()
        {
            wPresc = new WDrugPresc(this);
            cPresc = new CDrugPresc(this);
            examApply = new ExamApply(this);
            examApply.ShowApplyAppendInfoWindow = true;
            labTestApply = new LabApply(this);
            treatApply = new TreatApply(this);
            orderApply = new OrderApply(this);
            tjVsPriceDal = new TjVsPriceDal();
            outpMrDal = new OutpMrDal();
            OutpMrBusiness = new OutpMrBusiness();
            diagnosisDal = new OutpDiagnosisDal();
            DeptDictDal = new DeptDictDal();
            AddOrders = new List<OUTP_ORDERS_STANDARD>();
            responseWrapper = new ResponseWrapper();
            priceListDal = new PriceListDal();
            RationalUserOfDrugs = new RationalUseOfDrugs(PrescParameter.PassFirm, GlobalValue.AppCode, SystemParm.HisUnitCode, Constants.PASS_CHECK_MODE, Constants.PASS_HIS_CODE, SystemParm.LoginUser.USER_NAME, new Interface.api.models.RUD_DoctorInfo
            {
                deptCode = GlobalValue.DeptCode,
                deptName = GlobalValue.DeptName,
                empNo = SystemParm.LoginUser.EMP_NO,
                doctCode = SystemParm.LoginUser.USER_NAME,
                doctName = SystemParm.LoginUser.NAME,
                titleCode = SystemParm.LoginUser.COMMUNITY_CODE,
                titleName = SystemParm.LoginUser.TITLE
            }
                    );
            ;
            this.RationalUserOfDrugs.Init(Constants.PASS_CHECK_MODE, Constants.PASS_HIS_CODE, SystemParm.LoginUser.USER_NAME);
        }
        public OutpPatientInfo PatientInfo { get; set; }

        public IPresc wPresc { get; set; }
        public ICPresc cPresc { get; set; }
        public IApply examApply { get; set; }
        public IApply labTestApply { get; set; }
        public IApply treatApply { get; set; }
        public IApply orderApply { get; set; }
        private TjVsPriceDal tjVsPriceDal { get; set; }
        private OutpMrDal outpMrDal { get; set; }
        private OutpDiagnosisDal diagnosisDal { get; set; }
        private IOrderVersion orderVersion;
        private OutpMr MrInfo { get; set; }
        private IOutpMr OutpMrBusiness { get; set; }
        private DataTable DtDiagnosis { get; set; }
        public List<OUTP_ORDERS_STANDARD> AddOrders { get; set; }
        private DeptDictDal DeptDictDal { get; set; }
        private ResponseWrapper responseWrapper { get; set; }
        private PriceListDal priceListDal { get; set; }
        protected RationalUseOfDrugs RationalUserOfDrugs { get; set; }
        // 毒麻药品代办人信息
        private DataTable dtOutpDeputyInfo { get; set; }

        private PrescInputSvr prescInputSrv = new PrescInputSvr();
        private IOrderVersion OrderVersion
        {
            get
            {
                if (orderVersion == null)
                {
                    orderVersion = DataVersionFactory.GetOrderVersion();
                }
                return orderVersion;
            }
        }


        public OUTP_ORDERS_STANDARD Add()
        {
            int oldMaxOrderNo = this.MaxOrderNo;
            OUTP_ORDERS_STANDARD order = this.New();

            // 修复：确保ORDER_NO的唯一性，检查内存中已添加的医嘱
            int nextOrderNo = this.MaxOrderNo + 1;
            if (this.AddOrders != null && this.AddOrders.Count > 0)
            {
                int memoryMaxOrderNo = this.AddOrders.Max(o => (int)o.ORDER_NO);
                nextOrderNo = Math.Max(nextOrderNo, memoryMaxOrderNo + 1);
            }

            this.MaxOrderNo = nextOrderNo;
            order.ORDER_NO = this.MaxOrderNo;
            order.ORDER_SUB_NO = 1;
            // 修复：确保ITEM_NO的唯一性
            order.ITEM_NO = GetNextItemNo();
            order.OrderCosts = new List<OUTP_ORDERS_COSTS_STANDARD>();
            this.AddOrders.Add(order);

            // 日志：记录ORDER_NO分配信息
            LogMaxOrderNoChange("OrderBusiness.Add", oldMaxOrderNo, this.MaxOrderNo, $"MaxOrderNo递增（内存中医嘱数量: {this.AddOrders.Count - 1}）");
            LogOrderNoAssignment("OrderBusiness.Add", order, "新增医嘱");

            return order;
        }

        public OUTP_ORDERS_STANDARD Add(OUTP_ORDERS_STANDARD order)
        {
            this.AddOrders.Add(order);
            return order;
        }

        public OUTP_ORDERS_STANDARD New()
        {
            if (this.MrInfo == null)
            {
                throw new MessageException("请先保存患者病历");
            }
            if (this.DtDiagnosis == null || this.DtDiagnosis.Rows.Count == 0)
            {
                throw new MessageException("请先保存诊断信息");
            }
            OUTP_ORDERS_STANDARD order = new OUTP_ORDERS_STANDARD();
            SetPatientInfo(order);
            SetDoctorInfo(order);
            order.CHARGE_INDICATOR = 0;
            order.APPOINT_NO = "";
            order.SPLIT_FLAG = 0;
            order.REPETITION = 1;
            order.STATE = Constants.NEW_ORDER_STATE_STR;
            order.DIAGNOSIS_DESC = this.MrInfo?.DIAG_DESC;
            return order;
        }

        public void BingOrdersCosts(List<OUTP_ORDERS_STANDARD> orders, List<OUTP_ORDERS_COSTS_STANDARD> orderCosts)
        {
            orders.ForEach(order =>
            {
                order.OrderCosts = orderCosts.Where(costs => order.ORDER_NO == costs.ORDER_NO && order.ORDER_SUB_NO == costs.ORDER_SUB_NO).ToList();
                order.OrderCosts.Sort((cost1, cost2) => cost1.ITEM_NO.CompareTo(cost2.ITEM_NO));
            });
        }

        public void ChangePatient(OutpPatientInfo patientInfo, OutpMr outpMr)
        {
            this.PatientInfo = patientInfo;
            this.MrInfo = outpMr;
            this.SetDiagnosis();
            this.labTestApply.SetPatientInfo(patientInfo);
            this.examApply.SetPatientInfo(patientInfo);
            this.treatApply.SetPatientInfo(patientInfo);
            this.cPresc.SetPatientInfo(patientInfo);
            this.wPresc.SetPatientInfo(patientInfo);
            this.examApply.DefaultClinSymp = string.Empty;
            this.examApply.DefaultPhysSign = string.Empty;
            this.AddOrders.Clear();
            this.RationalUserOfDrugs.InitPatientInfo(new Interface.api.models.RUD_PatientInfo
            {
                pcPatCode = patientInfo.PATIENT_ID,
                pcInHospNo = patientInfo.CLINIC_NO,
                pcVisitCode = patientInfo.VISIT_ID.ToString("0"),
                pcName = patientInfo.NAME,
                pcSex = patientInfo.SEX,
                pcBirthday = patientInfo.DATE_OF_BIRTH.ToString(),
                pcHeightCM = "",
                pcWeighKG = "",
                pcDeptCode = GlobalValue.DeptCode,
                pcDeptName = GlobalValue.DeptName,
                pcDoctorCode = SystemParm.LoginUser.USER_NAME,
                pcDoctorName = SystemParm.LoginUser.NAME,
                piPatStatus = 2,
                piIsLactation = -1,
                piIsPregnancy = -1,
                pcPregStartDate = "",
                piHepDamageDegree = -1,
                piRenDamageDegree = -1,
                CLINIC_NO = patientInfo.CLINIC_NO,
                ID_NO = patientInfo.ID_NO,
                VISIT_DATE = patientInfo.VISIT_DATE,

            }, new Dictionary<string, string>
            {

            });
        }


        public void SetOutpMr(OutpMr outpMr)
        {
            this.MrInfo = outpMr;
        }
        public DataTable GetDtDiagnosis()
        {
            return this.DtDiagnosis;
        }
        public void SetDiagnosis()
        {
            if (null == this.MrInfo)
            {
                return;
            }
            this.DtDiagnosis = diagnosisDal.GetList(this.MrInfo.CLINIC_NO, this.MrInfo.ORDINAL.ToInt());
        }

        public bool Delete(List<OUTP_ORDERS_STANDARD> orders)
        {
            if (orders.Count == 0)
            {
                return true;
            }
            //先删除未保存的数据
            List<OUTP_ORDERS_STANDARD> orderTemp = orders.Where(order => order.STATE.Equals(Constants.NEW_ORDER_STATE_STR) && order.ISCHECK == 1).ToList();
            orderTemp.ForEach(order => orders.Remove(order));
            //再删除已经保存的数据
            if (orders.Count == 0)
            {
                return true;
            }
            //生成对应的删除语句进行删除
            List<string> sqls = new List<string>();
            List<DbParameter[]> parameters = new List<DbParameter[]>();
            Dictionary<string, string> dicSerialNO = new Dictionary<string, string>();
            foreach (var order in orders)
            {
                //验证开单医生
                IsisOrderedByCurrentUser(order);
                //验证是否已经收费
                IsOrderChargeed(order);
                //生成删除医嘱的语句
                OrderVersion.SetDeleteOrderSql(ref sqls, ref parameters, order);
                //生成删除费用明细的语句
                order.OrderCosts?.ForEach(cost =>
                {
                    OrderVersion.SetDeleteOrderCostsSql(ref sqls, ref parameters, cost);
                });

                switch (order.ORDER_CLASS)
                {
                    case OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE:
                        if (order.ORDER_NO == 1)
                        {
                            var groupOrders = orders.Where(o => o.ORDER_NO == order.ORDER_NO).ToList();
                            if (!IsSelectAllPresc(groupOrders, orders))
                            {
                                throw new MessageException("【" + order.ORDER_TEXT + "】为组合处方的第一个药品，不允许再删除!");
                            }
                        }
                        break;
                    case OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE:
                        //煎药
                        if(!string.IsNullOrEmpty(order.SERIAL_NO)&&!dicSerialNO.ContainsKey(order.SERIAL_NO))
                        {
                            dicSerialNO.Add(order.SERIAL_NO, order.SERIAL_NO);
                            treatApply.SetDeleteFreeFeeSqls(ref sqls, ref parameters, order.SERIAL_NO);
                        }
                        break;
                    case OrderClassDict.ORDER_CLASS_LAB:
                        labTestApply.CheckBillingIndicator(order);
                        if (order.APPOINT_NO != null)
                            labTestApply.SetDeleteSqls(ref sqls, ref parameters, order.APPOINT_NO);
                        break;
                    case OrderClassDict.ORDER_CLASS_EXAM:
                        examApply.CheckBillingIndicator(order);
                        if (order.APPOINT_NO != null)
                            examApply.SetDeleteSqls(ref sqls, ref parameters, order.APPOINT_NO);
                        break;
                    case OrderClassDict.ORDER_CLASS_TREAT:
                        treatApply.CheckBillingIndicator(order);
                        if (order.APPOINT_NO != null)
                            treatApply.SetDeleteSqls(ref sqls, ref parameters, order.APPOINT_NO);
                        break;
                    case OrderClassDict.ORDER_CLASS_BLOOD:
                        if (order.APPOINT_NO != null)
                        {
                            new BloodApplySrv().SetDeleteSqls(ref sqls, ref parameters, order.APPOINT_NO);
                        }
                        break;
                    case OrderClassDict.ORDER_CLASS_OPER:
                        if (order.APPOINT_NO != null)
                            SurgApplySrv.SetDeleteSqls(ref sqls, ref parameters, order.PATIENT_ID,order.APPOINT_NO);
                        break;
                    default:
                        break;

                }
            }
            bool saveState = this.db.Excute(sqls, parameters) > 0;
            if (saveState)
            {
                // 修复：删除成功后，从AddOrders集合中移除已删除的医嘱，避免重复检查时误报
                orders.ForEach(order => {
                    if (AddOrders.Contains(order))
                    {
                        AddOrders.Remove(order);
                    }
                });
            }
            return saveState;
        }

        public bool DeleteCosts(OUTP_ORDERS_STANDARD order, OUTP_ORDERS_COSTS_STANDARD cost)
        {
            bool saveState = true;
            saveState=order.OrderCosts.Remove(cost);
            if (!saveState)
            {
                return saveState;
            }
            //生成对应的删除语句进行删除
            List<string> sqls = new List<string>();
            List<DbParameter[]> parameters = new List<DbParameter[]>();
            SetCostAmount(0, order, cost);
            if (cost.STATE != Constants.NEW_ORDER_STATE_STR)
            {
                OrderVersion.SetDeleteOrderOneCostSql(ref sqls, ref parameters, cost, order.CHARGES);
                saveState = this.db.Excute(sqls, parameters) > 0;
            }
            return saveState;
        }

        public bool Save(List<OUTP_ORDERS_STANDARD> orders, List<OUTP_ORDERS_STANDARD> allOrders = null)
        {
            if (orders == null || orders.Count == 0)
            {
                LogCheckpoint("OrderBusiness.Save", "空医嘱列表", "直接返回true");
                return true;
            }

            // 日志：记录保存前的医嘱信息
            LogOrdersList("OrderBusiness.Save", orders, "保存前状态");
            LogCheckpoint("OrderBusiness.Save", "开始保存", $"医嘱数量: {orders.Count}, MaxOrderNo: {this.MaxOrderNo}");

            // 增强：保存前进行主键冲突预检查
            if (!PreCheckPrimaryKeyConflicts(orders))
            {
                LogError("OrderBusiness.Save", "主键冲突预检查失败", orders);
                throw new MessageException("检测到潜在的主键冲突，请重新操作或联系系统管理员");
            }

            if (OutpMrBusiness.CheckEmrFile(PatientInfo) == false)
            {
                throw new MessageException("门诊病历未填写不允许开检查，检验，处方处置！");
            }

            this.VerifyOrderData(orders);
            //List<OUTP_ORDERS_STANDARD> otherOrders = orders.
            //    Where(
            //    c => 
            //        c.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE) ||
            //        c.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_LAB) ||
            //        c.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_EXAM) ||
            //        c.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_TREAT)
            //    ).ToList();

            //军队医改2022
            string itemName = "";
            foreach (OUTP_ORDERS_STANDARD order in orders)
            {
                if (order.RECIPETYPE != null && order.RECIPETYPE.Equals("2"))
                {
                    itemName += order.ORDER_TEXT + ";";
                }

            }

            if (!string.IsNullOrEmpty(itemName))
            {
                FrmArmyTZS frmArmyZLJL = new FrmArmyTZS();
                frmArmyZLJL.patientId = this.PatientInfo.PATIENT_ID;
                frmArmyZLJL.item_name = itemName;
                frmArmyZLJL.ShowDialog();
                if (frmArmyZLJL.DialogResult != DialogResult.OK)
                {
                    return false;
                }
            }

            List<OUTP_ORDERS_STANDARD> wPrescList = orders.Where(order => OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(order.ORDER_CLASS)).ToList();
            //频次按周，用药说明必填
            List<OUTP_ORDERS_STANDARD> weekPrescList = wPrescList.Where(order => order.FREQ_INTERVAL_UNIT == "周"&&(string.IsNullOrEmpty(order.FREQ_DETAIL))).ToList();
            if(weekPrescList.Count>0)
            {
                throw new MessageException("按周执行需写明医生用药说明!");
            }
            List<OUTP_ORDERS_STANDARD> cPrescList = orders.Where(order => OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(order.ORDER_CLASS)).ToList();
            wPresc.AutoDividePresc(wPrescList, allOrders);
            cPresc.AutoDividePresc(cPrescList, allOrders);
            if (cPrescList.Count > orders.Where(order => OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(order.ORDER_CLASS)).ToList().Count)
            {
                foreach (OUTP_ORDERS_STANDARD oos in cPrescList)
                {
                    if (oos.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE))
                        continue;
                    orders.Add(oos);
                }
            }
            if (allOrders != null)
            {
                if (!this.VerifyMaxChargesPerPresc(allOrders))
                {
                    return false;
                }
            }
            else
            {
                if (!this.VerifyMaxChargesPerPresc(orders))
                {
                    return false;
                }
            }
            if (wPrescList.Count > 0)
            {
                if (PrescParameter.PassFirm.Equals("壹途"))
                {
                    if(Invoke_YiTu.Check(PatientInfo, wPrescList.ToArray(), DtDiagnosis.Rows[0]["DIAGNOSIS_CODE"].ToString(), DtDiagnosis.Rows[0]["DIAGNOSIS_DESC"].ToString(),GlobalValue.DeptCode,GlobalValue.DeptName) < 0)
                    {
                        return false;
                    }
                }
                else
                {
                    int status = 0;
                    status = this.RationalUserOfDrugs.AddScreenDrug(wPrescList.ToArray(), GlobalValue.DeptCode, GlobalValue.DeptName, SystemParm.LoginUser.USER_NAME, SystemParm.LoginUser.USER_NAME);
                    if (status != 1)
                    {
                        throw new MessageException("合理用药传入药品信息调用失败");
                    }
                    int[] checkResults = RationalUserOfDrugs.CheckDrug(wPrescList.ToArray(), GlobalValue.DeptCode, GlobalValue.DeptName, SystemParm.LoginUser.USER_NAME, SystemParm.LoginUser.USER_NAME);
                    for (int i = 0; i < checkResults.Length; i++)
                    {
                        
                    }
                }                

                // 毒麻处方属性对应显示需要填写的框框
                string dateVisit = StringHelper.GetOraDate("1900-01-01 00:00:01");
                this.dtOutpDeputyInfo = this.prescInputSrv.GetOutpDeputyInfo(dateVisit, "-100", "-1000");
                this.DeputyInfo(PatientInfo, wPrescList);

                // 检测毒麻信息是否有填写
                List<string> toxiP = new List<string> { "毒麻处方", "精神二类处方" };

                List<OUTP_ORDERS_STANDARD> toxiList = wPrescList.AsEnumerable().Where(r => toxiP.IndexOf(r.PRESC_ATTR.ToString("")) >= 0).ToList();
                foreach (OUTP_ORDERS_STANDARD toxi in toxiList)
                {
                    string presc_serial_no = toxi.OUTP_SERIAL_NO;
                    DataTable dt_DeputyInfo = this.prescInputSrv.GetOutpDeputyInfo(StringHelper.GetOraDate(PatientInfo.VISIT_DATE.ToString()), PatientInfo.VISIT_NO.ToString(), presc_serial_no);
                    if (dt_DeputyInfo == null || dt_DeputyInfo.Rows.Count == 0)
                    {
                        XtraMessageBox.Show("处方serial_no:" + presc_serial_no + "中有毒麻药或精神二类药：" + toxi.ORDER_TEXT + "，没有填写代办人信息，请填写!", "提示");
                        return false;
                    }
                }
            }
            //if (IsShowConfirmDialog && otherOrders.Count > 0)
            //{
            //    FrmOrdersConfirm ordersConfirm = new FrmOrdersConfirm();
            //    ordersConfirm.ShowConfirm(this.PatientInfo, orders, this.MrInfo, DtDiagnosis, this);
            //    if (ordersConfirm.DialogResult != DialogResult.OK)
            //    {
            //        return false;
            //    }
            //}

            List<OUTP_ORDERS_STANDARD> examList = orders.Where(order => OrderClassDict.ORDER_CLASS_EXAM.Equals(order.ORDER_CLASS)).ToList();
            List<OUTP_ORDERS_STANDARD> labTestList = orders.Where(order => OrderClassDict.ORDER_CLASS_LAB.Equals(order.ORDER_CLASS)).ToList();
            List<OUTP_ORDERS_STANDARD> treatList = orders.Where(order => OrderClassDict.ORDER_CLASS_TREAT.Equals(order.ORDER_CLASS)).ToList();
            examApply.AutoDivideApply(examList);
            labTestApply.AutoDivideApply(labTestList);

            // 检验项目计算几采几管费用信息
            if (labTestApply is LabApply)
            {
                (labTestApply as LabApply).CalculateJCJGCost(labTestList);
            }

            treatApply.AutoDivideApply(treatList);
            //这里处理非申请类的医嘱
            //orderApply.AutoDivideApply(orders.Where(order => order.APPOINT_NO.Equals("")).ToList());
            orderApply.AutoDivideApply(orders.Where(order => string.IsNullOrEmpty(order.APPOINT_NO)).ToList());
            List<string> sqls = new List<string>();
            List<DbParameter[]> dbParameters = new List<DbParameter[]>();
            DateTime sysDate = Convert.ToDateTime(PlatCommon.Common.PublicFunction.GetSysDate());
            List<string> serialNos = new List<string>();

            // 修复：不重新分配ITEM_NO，保持Add()方法中的分配
            // int itemNO=OrderVersion.GetMaxItemNo(orders[0].SERIAL_NO, orders[0].ORDER_CLASS);

            foreach (OUTP_ORDERS_STANDARD order in orders)
            {
                if (order.STATE == Constants.NEW_ORDER_STATE_STR)
                {
                    order.ORDER_DATE = sysDate;
                }

                // 修复：不重新分配ITEM_NO，保持Add()方法中的分配
                // order.ITEM_NO = itemNO++;

                // 日志：记录保存时的ITEM_NO状态
                LogCheckpoint("OrderBusiness.Save", "保存时ITEM_NO", $"ORDER_NO: {order.ORDER_NO}, ORDER_SUB_NO: {order.ORDER_SUB_NO}, ITEM_NO: {order.ITEM_NO}, ORDER_TEXT: {order.ORDER_TEXT}");
                if (order.examAppoints != null)
                {
                    order.examAppoints.REQ_DATE_TIME = sysDate;
                }
                if (order.labTestMaster != null)
                {
                    order.labTestMaster.REQUESTED_DATE_TIME = sysDate;
                }
                foreach (OUTP_ORDERS_COSTS_STANDARD orcs in order.OrderCosts)
                {
                    string itemCode = orcs.ITEM_CODE.ToString();
                    string itemClass = orcs.ITEM_CLASS.ToString();
                    responseWrapper = priceListDal.GetPriceListBy(itemCode, itemClass, SystemParm.HisUnitCode);
                    if (!responseWrapper.success)
                        return false;
                    DataTable dtPrice = (DataTable)responseWrapper.data;
                    if (dtPrice.Rows.Count <= 0)
                    {
                        return false;
                    }
                    orcs.CLASS_ON_RCPT = dtPrice.Rows[0]["class_on_outp_rcpt"].ToString();
                    orcs.CLASS_ON_RECKONING = dtPrice.Rows[0]["class_on_reckoning"].ToString();
                    orcs.SUBJ_CODE = dtPrice.Rows[0]["subj_code"].ToString();
                }

                switch (order.STATE)
                {
                    case "保存":

                        //生成医嘱的语句
                        OrderVersion.SetUpdateOrderSql(ref sqls, ref dbParameters, order);
                        //生成费用明细的语句
                        order.OrderCosts?.ForEach(cost =>
                        {
                            // 日志：记录费用记录的详细信息
                            LogCheckpoint("OrderBusiness.Save", "费用记录处理", $"ORDER_TEXT: {order.ORDER_TEXT}, ORDER_NO: {cost.ORDER_NO}, ORDER_SUB_NO: {cost.ORDER_SUB_NO}, ITEM_NO: {cost.ITEM_NO}, STATE: {cost.STATE}");

                            if (cost.STATE == Constants.NEW_ORDER_STATE_STR)
                            {
                                OrderVersion.SetInsertOrderCostsSql(ref sqls, ref dbParameters, cost);
                            }
                            else
                            {
                                OrderVersion.SetUpdateOrderCostsSql(ref sqls, ref dbParameters, cost);
                            }
                        });
                        break;
                    default:
                        if (!serialNos.Contains(order.SERIAL_NO))
                        {
                            serialNos.Add(order.SERIAL_NO);
                            OrderVersion.SetInsertOutpOrderSql(ref sqls, ref dbParameters, order);
                        }
                        //生成医嘱的语句
                        OrderVersion.SetInsertOrderSql(ref sqls, ref dbParameters, order);
                        //生成费用明细的语句
                        order.OrderCosts?.ForEach(cost =>
                        {
                            // 日志：记录费用记录的详细信息
                            LogCheckpoint("OrderBusiness.Save", "费用记录处理", $"ORDER_TEXT: {order.ORDER_TEXT}, ORDER_NO: {cost.ORDER_NO}, ORDER_SUB_NO: {cost.ORDER_SUB_NO}, ITEM_NO: {cost.ITEM_NO}, STATE: {cost.STATE}");

                            OrderVersion.SetInsertOrderCostsSql(ref sqls, ref dbParameters, cost);
                        });
                        break;
                }

            }
            examApply.SetSaveSqls(ref sqls, ref dbParameters, examList, this.PatientInfo);
            labTestApply.SetSaveSqls(ref sqls, ref dbParameters, labTestList, this.PatientInfo);
            treatApply.SetSaveSqls(ref sqls, ref dbParameters, orders.Where(order => OrderClassDict.ORDER_CLASS_TREAT.Equals(order.ORDER_CLASS)).ToList(), this.PatientInfo);

            // 日志：记录数据库执行前状态
            LogCheckpoint("OrderBusiness.Save", "数据库执行前", $"SQL语句数量: {sqls?.Count ?? 0}");

            bool saveState = false;
            try
            {
                saveState = this.db.Excute(sqls, dbParameters) > 0;
                LogCheckpoint("OrderBusiness.Save", "数据库执行", $"执行结果: {saveState}");
            }
            catch (Exception ex)
            {
                LogError("OrderBusiness.Save", $"数据库执行异常: {ex.Message}", orders);
                throw;
            }
            if (saveState)
            {
                #region CA

                if (PlatCommon.SysBase.SystemParm.LoginUser.CA_ENABLED == "1")
                {
                    foreach (OUTP_ORDERS_STANDARD order in orders)
                    {
                        if (!(order.STATE == Constants.NEW_ORDER_STATE_STR || order.STATE == Constants.UPDATE_ORDER_STATE_STR))
                            continue;
                        Tjhis.Interface.CA.CaBusiness.CaSignDataType signType= Tjhis.Interface.CA.CaBusiness.CaSignDataType.OutpDrugPresc;
                        string title = "门诊西药处方";
                        if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_EXAM)
                        {
                            signType = Tjhis.Interface.CA.CaBusiness.CaSignDataType.OutpExam;
                            title = "门诊检查";
                        }
                        else if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_LAB)
                        {
                            signType = Tjhis.Interface.CA.CaBusiness.CaSignDataType.OutpLab;
                            title = "门诊检验";
                        }
                        else if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE)
                        {
                            signType = Tjhis.Interface.CA.CaBusiness.CaSignDataType.OutpDrugPresc;
                            title = "门诊西药处方";
                        }
                        else if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE)
                        {
                            signType = Tjhis.Interface.CA.CaBusiness.CaSignDataType.OutpCnDrugPresc;
                            title = "门诊中药处方";
                        }
                        else if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_TREAT)
                        {
                            signType = Tjhis.Interface.CA.CaBusiness.CaSignDataType.OutpTreat;
                            title = "门诊处置";
                        }
                        else if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_OPER)
                        {
                            signType = Tjhis.Interface.CA.CaBusiness.CaSignDataType.OutpOperation;
                            title = "门诊手术";
                        }
                        else if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_BLOOD)
                        {
                            signType = Tjhis.Interface.CA.CaBusiness.CaSignDataType.OutpBloodUse;
                            title = "门诊用血申请";
                        }
                        DataTable dtOrder = GetOrdersDt(order.VISIT_NO.ToString(), order.VISIT_DATE.Value, order.ORDER_NO.ToString());
                        PlatCommon.SysBase.SystemParm.CaBusiness.CASignData(PlatCommon.SysBase.SystemParm.HisUnitCode
                        , "OUTPDOCT", PatientInfo.VISIT_DEPT, signType,
                        PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, title, dtOrder);
                    }
                }
                #endregion

                // 保存成功后重置ITEM_NO缓存
                ResetItemNoCache();
                CDrugPresc.ResetItemNoCache();
                WDrugPresc.ResetItemNoCache();
                this.AddOrders.Clear();

                // 日志：记录保存成功
                LogCheckpoint("OrderBusiness.Save", "保存成功", $"已重置缓存，MaxOrderNo: {this.MaxOrderNo}");
            }
            else
            {
                // 日志：记录保存失败
                LogError("OrderBusiness.Save", "保存失败", orders);
            }
            return saveState;
        }

        /// <summary>
        /// 毒麻处方信息处理
        /// </summary>
        private int DeputyInfo(OutpPatientInfo pi, List<OUTP_ORDERS_STANDARD> addOrders)
        {
            //if (!CommonMethod.StringIsNull(this.gvPresc.GetFocusedRowCellValue("SERIAL_NO").ToString("")))
            //    return 1;

            //int count = this.dtPresc.Compute("count(VISIT_NO)", "SERIAL_NO =DBNull.Value or SERIAL_NO='' or SERIAL_NO = '" + string.Empty+"'").ToInt(0);
            //DataRow[] rows = this.dtPresc.Select("SERIAL_NO is null or SERIAL_NO='' or SERIAL_NO = '" + string.Empty + "'");
            //int count = rows == null ? 0 : rows.Length;
            //if (count <= 0)
            //    return 1;

            if (!this.dtOutpDeputyInfo.Rows.Count.Equals(0))
            {
                for (int rowNo = this.dtOutpDeputyInfo.Rows.Count - 1; rowNo >= 0; rowNo--)
                {
                    if (this.dtOutpDeputyInfo.Rows[rowNo].RowState.Equals(DataRowState.Deleted))
                    {
                        continue;
                    }

                    if (String.IsNullOrEmpty(this.dtOutpDeputyInfo.Rows[rowNo]["DEPUTY_NAME"].ToString("")))
                    {
                        this.dtOutpDeputyInfo.Rows[rowNo].Delete();
                    }
                }
            }

            List<string> toxiP = new List<string>() { "毒麻处方", "精神二类处方" };


            int count = addOrders.AsEnumerable().Where(r => toxiP.IndexOf(r.PRESC_ATTR.ToString("")) >= 0).ToList().Count;

            if (count > 0)
            {
                if (this.dtOutpDeputyInfo.Rows.Count.Equals(0))
                {
                    DataTable table = this.prescInputSrv.GetOutpDeputyInfoRow(StringHelper.GetOraDate(pi.VISIT_DATE), pi.VISIT_NO.ToString());
                    if (table.Rows.Count.Equals(0))
                    {
                        XtraMessageBox.Show("处方中有毒麻药或精神二类药，需要填写代办人信息!", "提示");
                        //调用ceDeputy的click事件
                        //this.ceDeputy_EditValueChanging(this.ceDeputy, new ChangingEventArgs(false, true));
                        //this.ceDeputy_Click(this.ceDeputy, new EventArgs());
                        MyEventArgs myEvent = new MyEventArgs();
                        myEvent.pi = pi;
                        myEvent.addOrders = addOrders.AsEnumerable().Where(r => toxiP.IndexOf(r.PRESC_ATTR.ToString("")) >= 0).ToList();
                        this.ceDeputy_Click(null, myEvent);
                        return -1;
                    }

                    if (string.IsNullOrEmpty(table.Rows[0]["DEPUTY_NAME"].ToString("")))
                    {
                        XtraMessageBox.Show("处方中有毒麻药或精神二类药，需要填写代办人信息!", "提示");
                        //调用ceDeputy的click事件
                        //this.ceDeputy_EditValueChanging(this.ceDeputy, new ChangingEventArgs(false, true));
                        //this.ceDeputy_Click(this.ceDeputy, new EventArgs());
                        MyEventArgs myEvent = new MyEventArgs();
                        myEvent.pi = pi;
                        myEvent.addOrders = addOrders.AsEnumerable().Where(r => toxiP.IndexOf(r.PRESC_ATTR.ToString("")) >= 0).ToList();
                        this.ceDeputy_Click(null, myEvent);
                        return -1;
                    }

                    if (XtraMessageBox.Show("处方中有毒麻药或精神二类药，本次就诊已经填写过代办人信息, 代办人为 " + table.Rows[0]["DEPUTY_NAME"].ToString("") + " ，\r\n是否本次处方也使用 " + table.Rows[0]["DEPUTY_NAME"].ToString("") + " 代办人信息？, 选择[否]重新录入代办人信息！", "提示", MessageBoxButtons.YesNo).Equals(DialogResult.No))
                    {
                        //调用ceDeputy的click事件
                        //this.ceDeputy_EditValueChanging(this.ceDeputy, new ChangingEventArgs(false, true));
                        //this.ceDeputy_Click(this.ceDeputy, new EventArgs());
                        MyEventArgs myEvent = new MyEventArgs();
                        myEvent.pi = pi;
                        myEvent.addOrders = addOrders.AsEnumerable().Where(r => toxiP.IndexOf(r.PRESC_ATTR.ToString("")) >= 0).ToList();
                        this.ceDeputy_Click(null, myEvent);
                        return -1;
                    }

                    string sequenceNo = this.prescInputSrv.GetDeputySequenceNo();
                    if (string.IsNullOrEmpty(sequenceNo))
                    {
                        XtraMessageBox.Show("取毒麻、精神类处方流水号出错!!");
                        return -1;
                    }

                    DataRow row = this.dtOutpDeputyInfo.NewRow();
                    row["VISIT_DATE"] = pi.VISIT_DATE;
                    row["VISIT_NO"] = pi.VISIT_NO;
                    row["PATIENT_ID"] = table.Rows[0]["PATIENT_ID"].ToString("");
                    row["DEPUTY_ENABLED"] = 1;
                    row["SEQUENCE_NO"] = sequenceNo;
                    row["DEPUTY_NAME"] = table.Rows[0]["DEPUTY_NAME"].ToString("");
                    row["DEPUTY_SEX"] = table.Rows[0]["DEPUTY_SEX"].ToString("");
                    row["DEPUTY_DATE_OF_BIRTH"] = table.Rows[0]["DEPUTY_DATE_OF_BIRTH"].ToDateTime();
                    row["DEPUTY_CERTIFICATE_TYPE"] = table.Rows[0]["DEPUTY_CERTIFICATE_TYPE"].ToString("");
                    row["DEPUTY_ID"] = table.Rows[0]["DEPUTY_ID"].ToString("");
                    row["MEMO"] = table.Rows[0]["MEMO"].ToString("");
                    row["NEXT_OF_KIN_ADDR"] = table.Rows[0]["NEXT_OF_KIN_ADDR"].ToString("");
                    row["NEXT_OF_KIN_PHONE"] = table.Rows[0]["NEXT_OF_KIN_PHONE"].ToString("");
                    row["PRESC_SERIAL_NO"] = addOrders[0].OUTP_SERIAL_NO;
                    this.dtOutpDeputyInfo.Rows.Add(row);

                    bool rtnValue = DataServerBase.SaveTable(this.dtOutpDeputyInfo);
                    if (!rtnValue)
                    {
                        XtraMessageBox.Show("保存代办人信息出错!", "警告");
                    }
                }
                else
                {
                    DataRow row = this.dtOutpDeputyInfo.Rows[0];
                    if (string.IsNullOrEmpty(row["DEPUTY_NAME"].ToString("")) || string.IsNullOrEmpty(row["DEPUTY_SEX"].ToString("")) || string.IsNullOrEmpty(row["DEPUTY_CERTIFICATE_TYPE"].ToString("")) || string.IsNullOrEmpty(row["DEPUTY_ID"].ToString("")) || string.IsNullOrEmpty(row["NEXT_OF_KIN_PHONE"].ToString("")) || string.IsNullOrEmpty(row["NEXT_OF_KIN_ADDR"].ToString("")))
                    {
                        MyEventArgs myEvent = new MyEventArgs();
                        myEvent.pi = pi;
                        myEvent.addOrders = addOrders.AsEnumerable().Where(r => toxiP.IndexOf(r.PRESC_ATTR.ToString("")) >= 0).ToList();
                        this.ceDeputy_Click(null, myEvent);
                        //this.ceDeputy_Click(this.ceDeputy, new EventArgs());
                        return -1;
                    }
                }

            }

            return 1;
        }

        /// <summary>
        /// 代办人点击(checkbox)
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ceDeputy_Click(object sender, EventArgs e)
        {
            MyEventArgs myEvent = (MyEventArgs)e;
            OutpPatientInfo pi = myEvent.pi;
            List<OUTP_ORDERS_STANDARD> addOrders = myEvent.addOrders;

            //if (this.gvPresc.FocusedRowHandle < 0)
            //{
            //    return;
            //}

            DeputyInfoItem item = new DeputyInfoItem()
            {
                PRESC_SERIAL_NO = addOrders[0].OUTP_SERIAL_NO == null ? "" : addOrders[0].OUTP_SERIAL_NO.ToString(),
                PATIENT_ID = pi.PATIENT_ID,
                VISIT_DATE = pi.VISIT_DATE,
                VISIT_NO = pi.VISIT_NO
            };

            int count = this.dtOutpDeputyInfo.Rows.Count;
            if (count.Equals(0))
            {
                item.IS_NEWRECORD = "1";
            }
            else
            {
                item.IS_NEWRECORD = "0";
                item.DEPUTY_NAME = this.dtOutpDeputyInfo.Rows[count - 1]["DEPUTY_NAME"].ToString("");
                item.DEPUTY_SEX = this.dtOutpDeputyInfo.Rows[count - 1]["DEPUTY_SEX"].ToString("");
                item.DEPUTY_DATE_OF_BIRTH = this.dtOutpDeputyInfo.Rows[count - 1]["DEPUTY_DATE_OF_BIRTH"].ToDateTime();
                item.DEPUTY_CERTIFICATE_TYPE = this.dtOutpDeputyInfo.Rows[count - 1]["DEPUTY_CERTIFICATE_TYPE"].ToString("");
                item.DEPUTY_ID = this.dtOutpDeputyInfo.Rows[count - 1]["DEPUTY_ID"].ToString("");
                item.MEMO = this.dtOutpDeputyInfo.Rows[count - 1]["MEMO"].ToString("");
                item.DEPUTY_ENABLED = this.dtOutpDeputyInfo.Rows[count - 1]["DEPUTY_ENABLED"].ToInt();
                item.SEQUENCE_NO = long.Parse(this.dtOutpDeputyInfo.Rows[count - 1]["SEQUENCE_NO"].ToString());
                item.NEXT_OF_KIN_ADDR = this.dtOutpDeputyInfo.Rows[count - 1]["NEXT_OF_KIN_ADDR"].ToString("");
                item.NEXT_OF_KIN_PHONE = this.dtOutpDeputyInfo.Rows[count - 1]["NEXT_OF_KIN_PHONE"].ToString("");
            }

            FrmDeputyInfo frmDeputy = new FrmDeputyInfo(item, pi);
            CommonMethod.ShowCoverFrm();
            frmDeputy.ShowDialog();
            CommonMethod.CloseCoverFrm();

            if (string.IsNullOrEmpty(item.PATIENT_ID.ToString("")))
            {
                return;
            }

            if (string.IsNullOrEmpty(item.PRESC_SERIAL_NO.ToString("")) && !string.IsNullOrEmpty(item.PATIENT_ID.ToString("")))
            {
                this.dtOutpDeputyInfo.Clear();
                DataRow row = this.dtOutpDeputyInfo.NewRow();

                row["VISIT_DATE"] = item.VISIT_DATE;
                row["VISIT_NO"] = item.VISIT_NO;
                row["PATIENT_ID"] = item.PATIENT_ID;
                row["SEQUENCE_NO"] = item.SEQUENCE_NO;
                //row["PRESC_SERIAL_NO"] =;
                row["DEPUTY_ENABLED"] = item.DEPUTY_ENABLED;
                row["DEPUTY_SEX"] = item.DEPUTY_SEX;
                row["DEPUTY_NAME"] = item.DEPUTY_NAME;
                row["DEPUTY_DATE_OF_BIRTH"] = item.DEPUTY_DATE_OF_BIRTH;
                row["DEPUTY_CERTIFICATE_TYPE"] = item.DEPUTY_CERTIFICATE_TYPE;
                row["DEPUTY_ID"] = item.DEPUTY_ID;
                row["MEMO"] = item.MEMO;
                row["NEXT_OF_KIN_PHONE"] = item.NEXT_OF_KIN_PHONE;
                row["NEXT_OF_KIN_ADDR"] = item.NEXT_OF_KIN_ADDR;
                this.dtOutpDeputyInfo.Rows.Add(row);
            }
        }

        private void VerifyOrderData(List<OUTP_ORDERS_STANDARD> orders)
        {
            foreach (var order in orders)
            {
                //这里验证配置好的非空内容
                ModelDataHelper.VerificationModel(order);
                if (order.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE))
                {
                    wPresc.Verify(order);
                }
                if (order.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE))
                {
                    cPresc.Verify(order);
                }
            }
        }

        private bool VerifyMaxChargesPerPresc(List<OUTP_ORDERS_STANDARD> orders)
        {
            Decimal maxCharges = 0M;
            Dictionary<string, Decimal?> chargeAmountDict = new Dictionary<string, decimal?>();
            // 单张处方最大费用金额（0-不校验）
            if (Decimal.TryParse(PrescParameter.MAX_CHARGES, out maxCharges))
            {
                if (maxCharges > 0)
                {
                    foreach (var order in orders)
                    {
                        if ("AB".Contains(order.ORDER_CLASS))
                        {
                            foreach (var cost in order.OrderCosts)
                            {
                                if (chargeAmountDict.ContainsKey(order.APPOINT_NO))
                                {
                                    chargeAmountDict[order.APPOINT_NO] += cost.COSTS;
                                }
                                else
                                {
                                    chargeAmountDict.Add(order.APPOINT_NO, cost.COSTS);
                                }
                            }
                        }
                    }

                    foreach (KeyValuePair<string, Decimal?> kv in chargeAmountDict)
                    {
                        if (kv.Value > maxCharges)
                        {
                            if (XtraMessageBox.Show("单张处方:" + kv.Key + "最大费用金额大于" + maxCharges + "，是否继续保存？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                            {
                                return false;
                            }
                        }
                    }
                }
            }
            return true;
        }

        #region 删除医嘱数据验证
        /// <summary>
        /// 验证医嘱是否已经收费
        /// </summary>
        /// <param name="dr"></param>
        /// <returns></returns>
        public bool IsOrderChargeed(OUTP_ORDERS_STANDARD drOrder)
        {
            int tCHARGE_INDICATOR = drOrder.CHARGE_INDICATOR.ToInt(0);
            if (!tCHARGE_INDICATOR.Equals(Constants.UN_CHARGE_INDICATOR_VALUE))
            {
                throw new MessageException("已做收费处理的项目不能进行删除操作!");
            }
            tCHARGE_INDICATOR = OrderVersion.ValidateOrderChargeIndicator(drOrder);
            if (!tCHARGE_INDICATOR.Equals(Constants.UN_CHARGE_INDICATOR_VALUE))
            {
                throw new MessageException("已做收费处理的项目不能进行删除操作!");
            }
            return false;
        }
        /// <summary>
        /// 验证是否是本人开的医嘱
        /// </summary>
        /// <param name="drOrder"></param>
        /// <returns></returns>
        private bool IsisOrderedByCurrentUser(OUTP_ORDERS_STANDARD order)
        {
            string ordered_by_doctor = order.DOCTOR_NO;
            string ls_drugname = order.ORDER_TEXT;
            if (!ordered_by_doctor.Equals(SystemParm.LoginUser.USER_NAME))
            {
                throw new MessageException("【" + ls_drugname + "】为其他医生所开的处方药品，不允许删除！");
            }
            return true;
        }
        private bool IsSelectAllPresc(List<OUTP_ORDERS_STANDARD> drPrescOrders, List<OUTP_ORDERS_STANDARD> delOrders)
        {
            bool isSelectedAll = true;
            foreach (OUTP_ORDERS_STANDARD order in drPrescOrders)
            {
                string orderSubNo = order.ORDER_SUB_NO.ToString();
                string orderNo = order.ORDER_NO.ToString();
                OUTP_ORDERS_STANDARD subOrder = delOrders.FirstOrDefault(t =>
                    OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(t.ORDER_CLASS)
                    && orderNo.Equals(t.ORDER_NO.ToString())
                    && orderSubNo.Equals(t.ORDER_SUB_NO.ToString()));
                if (subOrder == null)
                {
                    isSelectedAll = false;
                    break;
                }
            }
            return isSelectedAll;
        }

        #endregion

        public ResponseWrapper GetOrderNo(string hisUnitCode, string clinicNo)
        {
            string strSql = "select nvl(max(order_no),0)  from OUTPDOCT.OUTP_ORDERS_STANDARD where his_unit_code = :t1 and clinic_no = :t2";
            try
            {
                List<DbParameter> listPara = new List<DbParameter>();
                listPara.Add(db.CreateDbParameter(":t1", hisUnitCode));
                listPara.Add(db.CreateDbParameter(":t2", clinicNo));
                return ResponseWrapper.markSuccess(db.GetSingleValue(strSql, listPara));
            }
            catch (Exception ex)
            {
                return ResponseWrapper.markError("获取最大医嘱号." + ex.ToString());
            }
        }

        public string GetViewCosts(List<OUTP_ORDERS_STANDARD> orders)
        {
            List<OUTP_ORDERS_COSTS_STANDARD> orderCosts = new List<OUTP_ORDERS_COSTS_STANDARD>();
            foreach(OUTP_ORDERS_STANDARD order in orders.Where(r => r.STATE != "退费").ToList())
            {
                foreach(OUTP_ORDERS_COSTS_STANDARD cost in order.OrderCosts)
                {
                    orderCosts.Add(cost);
                }
            }
            string strResult = "总费用：{0}元（";
            if (orders == null || orders.Count == 0)
            {
                strResult += "检查：0元，检验：0元，药品：0元，其他：0元）;未缴费用：0元";
                return string.Format(strResult, "0");
            }

            var groups = orderCosts.GroupBy(g => g.ITEM_CLASS).Select(result => new { OrderClass = result.Key.ToString(), Costs = result.Sum(s => s.CHARGES) }).ToList();
            decimal? sumA = groups.FirstOrDefault(f => f.OrderClass.Equals(OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE))?.Costs;
            if (sumA > 0)
            {
                strResult += " 西药：" + sumA.ToDecimal(0).ToString("f2")+ "元，";
            }
            decimal? sumB = groups.FirstOrDefault(f => f.OrderClass.Equals(OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE))?.Costs;
            if (sumB.ToDecimal(0) > 0)
            {
                strResult += " 中药：" + sumB.ToDecimal(0).ToString("f2") + "元，";
            }
            decimal? sumC = groups.FirstOrDefault(f => f.OrderClass.Equals(OrderClassDict.ORDER_CLASS_LAB))?.Costs;
            if (sumC > 0)
            {
                strResult += " 检验：" + sumC.ToDecimal(0).ToString("f2") + "元，";
            }
            decimal? sumD = groups.FirstOrDefault(f => f.OrderClass.Equals(OrderClassDict.ORDER_CLASS_EXAM))?.Costs;
            if (sumD > 0)
            {
                strResult += " 检查：" + sumD.ToDecimal(0).ToString("f2") + "元，";
            }
            decimal? sumF = groups.FirstOrDefault(f => f.OrderClass.Equals(OrderClassDict.ORDER_CLASS_OPER) || f.OrderClass.Equals(OrderClassDict.ORDER_CLASS_OPER_G))?.Costs;
            if (sumF > 0)
            {
                strResult += " 手术：" + sumF.ToDecimal(0).ToString("f2") + "元，";
            }
            decimal totals = orderCosts.Sum(s => s.COSTS).ToDecimal(0);
            decimal totalsNo = totals - orderCosts.FindAll(f=> f.CHARGE_INDICATOR == 1).ToList().Sum(s => s.COSTS).ToDecimal(0);
            if (totals > 0)
            {
                decimal treat = totals - sumA.ToDecimal(0) - sumB.ToDecimal(0) - sumC.ToDecimal(0) - sumD.ToDecimal(0) - sumF.ToDecimal(0);
                strResult += " 处置：" + treat.ToString("f2") + "元，";
            }
            strResult=strResult.TrimEnd('，');
            strResult += "）;未缴费用：{1}元";
            return string.Format(strResult, totals.ToString("f2"), totalsNo.ToString("f2"));
        }
        public DataTable GetSelectOrderExist(string appointNo, string patientId)
        {
            string sql = " SELECT CLINIC_NO,ORDER_NO,ORDER_SUB_NO,DOCTOR_NO from OUTPDOCT.V_OUTP_ORDERS_STANDARD  WHERE APPOINT_NO = :appointNo AND PATIENT_ID = :patientId";
            List<DbParameter> listPara = new List<DbParameter>();
            listPara.Add(db.CreateDbParameter(":appointNo", appointNo));
            listPara.Add(db.CreateDbParameter(":patientId", patientId));
            return db.QueryList(sql, listPara);
        }

        public void GetDeleteOrders(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, string HisUnitCode, string clinicNo, string orderNo, string OrderSubNo, string OrderClass = "")
        {
            string sql = "delete from " + (string.IsNullOrEmpty(OrderClass) ? "outp_treat_rec" : "outp_presc") + "+  where his_unit_code = :hisUnitCode and  CLINIC_NO = :clinicNo and ORDER_NO = :orderNo and SUB_ORDER_NO = :OrderSubNo";
            sqlLst.Add(sql);
            List<DbParameter> listPara = new List<DbParameter>();
            listPara.Add(db.CreateDbParameter(":hisUnitCode", SystemParm.HisUnitCode));
            listPara.Add(db.CreateDbParameter(":clinicNo", clinicNo));
            listPara.Add(db.CreateDbParameter(":orderNo", orderNo));
            listPara.Add(db.CreateDbParameter(":OrderSubNo", OrderSubNo));
            listPara.Add(db.CreateDbParameter(":OrderClass", OrderClass));

            dbParametersLst.Add(listPara.ToArray());
        }

        public void GetDeleteOrdersCosts(ref List<string> sqlLst, ref List<DbParameter[]> dbParametersLst, string hisUnitCode, string clinicNo, string orderNo, string orderSubNo)
        {
            string sql = " delete from  OUTP_ORDERS_COSTS where his_unit_code = :hisUnitCode and  CLINIC_NO =:clinicNo and ORDER_NO = :orderNo and ORDER_SUB_NO = :orderSubNo";
            sqlLst.Add(sql);
            List<DbParameter> listPara = new List<DbParameter>();
            listPara.Add(db.CreateDbParameter(":hisUnitCode", SystemParm.HisUnitCode));
            listPara.Add(db.CreateDbParameter(":clinicNo", clinicNo));
            listPara.Add(db.CreateDbParameter(":orderNo", orderNo));
            listPara.Add(db.CreateDbParameter(":orderSubNo", orderSubNo));
            dbParametersLst.Add(listPara.ToArray());

        }

        public void AddDeleteIndOutpOrderCosts(ref Dictionary<string, string> idc, string HisUnitCode, string ClinicNo, string OrderNo, string OrderSubNo, string ItemNo)
        {

        }

        public void AddDeleteIndOutpOrderCosts(ref Dictionary<string, string> idc, string hisUnitCode, string clinicNo, string orderNo, string orderSubNo)
        {

        }
        public int GetMaxItemNo(string clinicNo)
        {
            string sql = "select nvl(max(order_no),0)  from outp_orders_standard where clinic_no = :clinicNo";
            List<DbParameter> listPara = new List<DbParameter>();
            listPara.Add(db.CreateDbParameter(":clinicNo", clinicNo));
            return Convert.ToInt32(db.GetSingleValue(sql, listPara).ToString());
        }
        public DataTable GetOrdersDt(string visit_no,DateTime visit_date,string order_no)
        {
            string sql =string.Format(@"select * from outp_orders_standard where visit_no ='{0}'
                        and visit_date=TO_DATE('{1}','yyyy-mm-dd HH24:mi:ss') and ORDER_NO in({2})"
                    , visit_no, visit_date.ToString("yyyy-MM-dd HH:mm:ss"), order_no);
            return db.SelectDataTable(sql, "OUTP_ORDERS_STANDARD");
        }
        public int set_outporders(ref Dictionary<string, string> idc, string PatientId, string ClinicNo, string OrderNo, string OrderSubNo, string OrderedBy, string DoctorName, string OrderDate, string DoctorNo, string Nurse, string Signatureno, string Usagedesc, string DiagnosisDesc, string HisUnitCode, string OutpSerialNo, string AppointNo, string OrderClass, string OrderText, string OrderCode, string Dosage, string DosageUnits, string Administration, string Frequency, string Skintest, string Prescpsno, decimal Amount, string Performedby, decimal Costs, decimal Charges, string SplitFlag, string ItemSpec, string FirmId, string Repetition, string PrescAttr, string Units, string Abidance, string PerformTimes, string FreqDetail, string BatchNo, string DosePerUnit, decimal _TRADE_PRICE, string _BATCH_CODE, string _GUID, decimal _ITEM_PRICE, int itemNo, string bjca_sn = "", string bjca_value = "", string bjca_time = "", string Decoction = "", string COUNT_PER_REPETITION = "null", string RECIPETYPE = "")
        {
            if (Amount == 0)
            {
                Amount = 1;
            }
            string visitDate;
            string visitNo;
            if (!GetVisitDateAndVisitNo(ClinicNo, out visitDate, out visitNo))
            {
                return -1;
            }
            string ChargeIndicator = "0";
            string AppointItemNo = "1";
            string skinResult = "";
            string RcptNo = "";
            string GetDrugFlag = "";
            string SkinFlag = "";
            string ProvidedIndicator = "";
            if ("AB".IndexOf(OrderClass) >= 0)//药品要返回插入OUTP_PRESC表
            {
                StringBuilder strSql = new StringBuilder("INSERT INTO OUTPDOCT.OUTP_PRESC");
                strSql.Append("(VISIT_DATE,VISIT_NO,SERIAL_NO,PRESC_NO,ITEM_NO,ITEM_CLASS,DRUG_CODE,DRUG_NAME,DRUG_SPEC,FIRM_ID,UNITS,AMOUNT,DOSAGE,DOSAGE_UNITS,ADMINISTRATION,FREQUENCY,PROVIDED_INDICATOR,COSTS,CHARGES,CHARGE_INDICATOR,DISPENSARY,REPETITION,ORDER_NO,SUB_ORDER_NO,FREQ_DETAIL,GETDRUG_FLAG,PRESC_ATTR,ABIDANCE,SKIN_FLAG,PRESC_PSNO,SKIN_RESULT,PERFORM_TIMES,RCPT_NO,HIS_UNIT_CODE,CLINIC_NO)");
                strSql.Append(" Values (");
                strSql.Append($"{visitDate},'{visitNo}','{OutpSerialNo}','{AppointNo}','{itemNo}','{OrderClass}','{OrderCode}','{OrderText}','{ItemSpec}','{FirmId}','{Units}','{Amount}','{Dosage}','{DosageUnits}','{Administration}','{Frequency}','{ProvidedIndicator}','{Costs}','{Charges}','{ChargeIndicator}','{Performedby}','{Repetition}','{OrderNo}','{OrderSubNo}','{FreqDetail}','{GetDrugFlag}','{PrescAttr}','{Abidance}','{SkinFlag}','{Prescpsno}','{skinResult}','{PerformTimes}','{RcptNo}','{HisUnitCode}','{ClinicNo}'");
                strSql.Append(")");
                idc.Add(strSql.ToString(), "插入OUTP_PRESC失败" + strSql.ToString());
            }
            else
            {
                StringBuilder strSql = new StringBuilder("INSERT INTO OUTPDOCT.OUTP_TREAT_REC");
                strSql.Append("(VISIT_DATE,VISIT_NO,SERIAL_NO,ITEM_NO,ITEM_CLASS,ITEM_CODE,ITEM_NAME,ITEM_SPEC,UNITS,AMOUNT,FREQUENCY,PERFORMED_BY,COSTS,CHARGES,CHARGE_INDICATOR,APPOINT_NO,APPOINT_ITEM_NO,SUB_ORDER_NO,ORDER_NO,FREQ_DETAIL,RCPT_NO,SKIN_RESULT,HIS_UNIT_CODE,CLINIC_NO)");
                strSql.Append(" Values (");
                strSql.Append($"{visitDate},'{visitNo}','{OutpSerialNo}','{itemNo}','{OrderClass}','{OrderCode}','{OrderText}','{ItemSpec}','{Units}','{Amount}','{Frequency}','{Performedby}','{Costs}','{Charges}','{ChargeIndicator}','{AppointNo}','{AppointItemNo}','{OrderSubNo}','{OrderNo}','{FreqDetail}','{RcptNo}','{skinResult}','{HisUnitCode}','{ClinicNo}'");
                strSql.Append(")");
                idc.Add(strSql.ToString(), "插入OUTP_TREAT_REC失败" + strSql.ToString());
            }
            return 0;
        }
        protected virtual bool GetVisitDateAndVisitNo(string clinicNo, out string visitDate, out string visitNo)
        {
            string sql = "SELECT  VISIT_DATE,VISIT_NO FROM CLINIC_MASTER WHERE CLINIC_NO = :clinic_no";

            List<DbParameter> listPara = new List<DbParameter>();
            listPara.Add(db.CreateDbParameter(":clinic_no", clinicNo));
            DataTable dtclinic = db.QueryList(sql, listPara);

            visitDate = "";
            visitNo = "";
            if (dtclinic.Rows.Count <= 0)
            {
                return false;
            }
            visitDate = "to_date('" + DateTime.Parse(dtclinic.Rows[0]["VISIT_DATE"].ToString()).ToString("yyyy-MM-dd") + "','yyyy-mm-dd')";
            visitNo = dtclinic.Rows[0]["VISIT_NO"].ToString();
            return true;
        }
        public int set_outporderscosts(ref Dictionary<string, string> idc, string PatientId, string clinicNo, string OrderNo, string OrderSubNo, string ItemNo, string ItemClass, string ItemName, string ItemCode, string ItemSpec, string Units, string Repetition, decimal Amount, string classOnRcpt, decimal costs, decimal charges, string rcptNo, string chargeIndicator, string classOnReckoning, string subjCode, string priceQuotiety, decimal itemPrice, string InsuranceFlag, string Clxzbs, string Clwzm, string hisUnitCode, string OutpSerialNo, string OrderClass, string PerformedBy, decimal _TRADE_PRICE, string _BATCH_CODE, string _GUID, string _BATCH_NO, string Ypxzbs = "", string req_dept = "", string req_doctno = "", string RECIPETYPE = "")
        {
            string visitDate;
            string visitNo;
            if (!GetVisitDateAndVisitNo(clinicNo, out visitDate, out visitNo))
            {
                return -1;
            }
            string repetition = "";
            StringBuilder strSql = new StringBuilder("INSERT INTO OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD");
            strSql.Append("(PATIENT_ID,VISIT_DATE,VISIT_NO,SERIAL_NO,ORDER_CLASS,ITEM_CLASS,ORDER_NO,ORDER_SUB_NO,ITEM_NO，ITEM_NAME,ITEM_CODE,ITEM_SPEC,UNITS,REPETITION,AMOUNT,ORDERED_BY_DEPT,ORDERED_BY_DOCTOR,PERFORMED_BY,CLASS_ON_RCPT,COSTS,CHARGES,RCPT_NO,CHARGE_INDICATOR,CLASS_ON_RECKONING,SUBJ_CODE,PRICE_QUOTIETY,ITEM_PRICE,CLINIC_NO,HIS_UNIT_CODE)");
            strSql.Append(" Values (");
            strSql.Append($"'{PatientId}',{visitDate},{visitNo},'{OutpSerialNo}','{ItemClass}','{ItemClass}','{OrderNo}','{OrderSubNo}','{ItemNo}','{ItemName}','{ItemCode}','{ItemSpec}','{Units}','{Repetition}','{Amount}','{req_dept}','{req_doctno}','{PerformedBy}','{classOnRcpt}','{costs}','{charges}','{rcptNo}','{chargeIndicator}','{classOnReckoning}','{subjCode}','{priceQuotiety}','{itemPrice}','{clinicNo}','{hisUnitCode}'");
            strSql.Append(")");
            idc.Add(strSql.ToString(), "插入OUTP_ORDERS_COSTS失败" + strSql.ToString());
            return 0;
        }

        public int SetOrder(ref Dictionary<string, string> idc, string patientId, string serialNo, string orderedBy, string doctor, string clinicNo, string doctorNo, string orderDate, string hisUnitCode)
        {
            if (!GetVisitDateAndVisitNo(clinicNo, out string visitDate, out string visitNo))
            {
                return -1;
            }
            StringBuilder strSql = new StringBuilder("Insert into OUTPDOCT.OUTP_ORDERS");
            strSql.Append("(PATIENT_ID, VISIT_DATE, VISIT_NO, SERIAL_NO, ORDERED_BY,DOCTOR,ORDER_DATE,CLINIC_NO, DOCTOR_NO,HIS_UNIT_CODE)");
            strSql.Append(" Values (");
            strSql.Append($"'{patientId}',{visitDate},'{visitNo}','{serialNo}','{orderedBy}','{doctor}',{orderDate},'{clinicNo}','{doctorNo}','{hisUnitCode}'");
            strSql.Append(")");
            idc.Add(strSql.ToString(), "插入OUTP_ORDERS失败！" + strSql.ToString());
            return 0;
        }
        public string GetSumOrderCosts(string hisUnitCode, string clinicNo, string orderNo, string orderSubNo)
        {
            string sql = "SELECT NVL(SUM(CHARGE_INDICATOR),0) FROM OUTPDOCT.V_OUTP_ORDERS_COSTS WHERE CLINIC_NO = :clinicNo AND ORDER_NO = :orderNo AND ORDER_SUB_NO = :orderSubNo";
            List<DbParameter> listPara = new List<DbParameter>();
            listPara.Add(db.CreateDbParameter(":clinicNo", clinicNo));
            listPara.Add(db.CreateDbParameter(":orderNo", orderNo));
            listPara.Add(db.CreateDbParameter(":orderSubNo", orderSubNo));
            return db.GetSingleValue(sql, listPara).ToString();
        }

        public void SetAmount(decimal amount, OUTP_ORDERS_STANDARD order)
        {
            string orderClass = order.ORDER_CLASS;
            if ("CDF".Contains(orderClass))
            {
                throw new MessageException("检查、检验及手术类不允许修改数量");
            }
            order.AMOUNT = amount;
            decimal orderCosts = 0;
            decimal orderCharges = 0;
            order.OrderCosts?.ForEach(costs =>
            {
                // 修复费用重复计算问题 - 2025-08-21
                costs.AMOUNT = amount;
                costs.COSTS = costs.ITEM_PRICE * costs.AMOUNT;

                // 应收费用：有系数时乘系数，无系数时等于计价费用
                if (costs.PRICE_QUOTIETY > 0 && costs.PRICE_QUOTIETY != 1)
                {
                    costs.CHARGES = costs.COSTS * costs.PRICE_QUOTIETY;
                }
                else
                {
                    costs.CHARGES = costs.COSTS;
                }

                orderCosts += costs.COSTS.ToDecimal(1);
                orderCharges += costs.CHARGES.ToDecimal(1);
            });
            order.COSTS = orderCosts;
            order.CHARGES = orderCharges;
        }

        public void SetCostAmount(decimal amount, OUTP_ORDERS_STANDARD order, OUTP_ORDERS_COSTS_STANDARD cost)
        {
            cost.AMOUNT = amount;
            decimal orderCosts = 0;
            decimal orderCharges = 0;
            order.OrderCosts?.ForEach(costs =>
            {
                if (costs.ITEM_NO == cost.ITEM_NO)
                {
                    costs.CHARGES = costs.ITEM_PRICE * costs.AMOUNT;
                    costs.COSTS = costs.ITEM_PRICE * costs.AMOUNT;
                }
                orderCosts += costs.COSTS.ToDecimal(1);
                orderCharges += costs.CHARGES.ToDecimal(1);
            });
            order.COSTS = orderCosts;
            order.CHARGES = orderCharges;
        }

        private void SetDoctorInfo(OUTP_ORDERS_STANDARD order)
        {
            order.DOCTOR = SystemParm.LoginUser.NAME;
            order.DOCTOR_NO = SystemParm.LoginUser.USER_NAME;
            order.HIS_UNIT_CODE = SystemParm.HisUnitCode;
        }

        private void SetPatientInfo(OUTP_ORDERS_STANDARD order)
        {
            order.PATIENT_ID = this.PatientInfo.PATIENT_ID;
            order.CLINIC_NO = this.PatientInfo.CLINIC_NO;
            order.VISIT_DATE = this.PatientInfo.VISIT_DATE;
            order.VISIT_NO = this.PatientInfo.VISIT_NO;
            order.ORDERED_BY = PatientInfo.REGISTER_DEPT;
        }
        public void SetMaxOrderNo(List<OUTP_ORDERS_STANDARD> orders)
        {
            int oldMaxOrderNo = this.MaxOrderNo;
            if (orders == null || orders.Count == 0)
            {
                this.MaxOrderNo = 0;
                LogMaxOrderNoChange("OrderBusiness.SetMaxOrderNo", oldMaxOrderNo, this.MaxOrderNo, "设置为0（空列表）");
                return;
            }
            this.MaxOrderNo = orders.Max(o => o.ORDER_NO).ToInt();
            LogMaxOrderNoChange("OrderBusiness.SetMaxOrderNo", oldMaxOrderNo, this.MaxOrderNo, $"根据现有医嘱设置（医嘱数量: {orders.Count}）");
        }

        public List<OUTP_ORDERS_STANDARD> GetOrdersList(string patientId, string clinicNo, DateTime visitDate, int visitNo)
        {
            return OrderVersion.GetOrdersList(patientId, clinicNo, visitDate, visitNo);
        }

        public List<OUTP_ORDERS_COSTS_STANDARD> GetOrderCostsList(string patientId, string clinicNo, DateTime visitDate, int visitNo)
        {
            return OrderVersion.GetOrderCostsList(patientId, clinicNo, visitDate, visitNo);
        }

        public List<OUTP_ORDERS_STANDARD> GetOrdersList()
        {
            if (PatientInfo == null)
                return new List<OUTP_ORDERS_STANDARD>();
            return OrderVersion.GetOrdersList(PatientInfo.PATIENT_ID, PatientInfo.CLINIC_NO, PatientInfo.VISIT_DATE, PatientInfo.VISIT_NO);
        }

        public List<OUTP_ORDERS_COSTS_STANDARD> GetOrderCostsList()
        {
            if (PatientInfo == null)
                return new List<OUTP_ORDERS_COSTS_STANDARD>();
            return OrderVersion.GetOrderCostsList(PatientInfo.PATIENT_ID, PatientInfo.CLINIC_NO, PatientInfo.VISIT_DATE, PatientInfo.VISIT_NO);
        }

        public void SetSubFlag(List<OUTP_ORDERS_STANDARD> orders)
        {
            this.wPresc.SetSubPrescFlag(orders);
            //foreach (var order in orders)
            //{
            //    decimal currentOrderNo = order.ORDER_NO;
            //    decimal currentSubOrderNo = order.ORDER_SUB_NO;
            //    int orderNoCount = orders.Where(w => w.ORDER_NO == currentOrderNo).Count();
            //    decimal tempMaxSubOrderNo = orders.Where(w => w.ORDER_NO == currentOrderNo).Max(m => m.ORDER_SUB_NO);
            //    string subOrderFlag = "";
            //    if (orderNoCount > 1)
            //    {
            //        //第一个
            //        if ("1".Equals(currentSubOrderNo))
            //        {
            //            subOrderFlag = string.Concat(Constants.COMPLEX_BEGIN, " ");
            //        }
            //        else if (tempMaxSubOrderNo.Equals(currentSubOrderNo))
            //        {
            //            subOrderFlag = string.Concat(Constants.COMPLEX_END, " ");
            //        }
            //        else
            //        {
            //            subOrderFlag = string.Concat(Constants.COMPLEX_AMONG, " ");
            //        }
            //    }
            //    order.SUB_FLAG = subOrderFlag;
            //}
        }

        public bool SetClinciItem(OUTP_ORDERS_STANDARD order, List<OUTP_ORDERS_STANDARD> addOrders, InputResult result,ref string strMsg)
        {
            bool addState = false;
            if (!CheckRepeat(order, addOrders, result))
            {
                return false;
            }
            order.ORDER_TEXT = result.ItemName;
            order.ORDER_CODE = result.ItemCode;
            order.INSUR_CODE = result.InsureCode;
            order.INSUR_NAME = result.InsureName;
            order.ITEM_SPEC = result.ItemPackageSpec;

            // 处理 performedBy 为空的情况，避免处方发药系统无法正常发药
            if (string.IsNullOrEmpty(result.Performed_dept))
            {
                // 如果 InputResult 的 Performed_dept 为空，需要根据项目类型设置默认执行科室
                // 对于药品类项目，这个问题特别重要，因为会影响价格计算和发药
                if (result.ItemClass == OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE ||
                    result.ItemClass == OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE)
                {
                    // 药品类项目必须有执行科室，否则无法正常发药
                    throw new MessageException($"药品【{result.ItemName}】的执行科室为空，无法确定发药科室！请检查药品字典配置或重新选择。");
                }
                else
                {
                    // 非药品项目可以使用当前登录科室作为默认执行科室
                    order.PERFORMED_BY = GlobalValue.DeptCode;
                }
            }
            else
            {
                order.PERFORMED_BY = result.Performed_dept;
            }

            order.ORDER_CLASS = result.ItemClass;
            order.DRUG_SPEC = result.ItemSpec;
            TJ_VS_PRICE tjVsPrice = tjVsPriceDal.GetModelBySql(new TJ_VS_PRICE { ITEM_CODE = result.ItemCode });
            switch (order.ORDER_CLASS)
            {
                case OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE:
                    addState = this.wPresc.AddDrug(this.PatientInfo, result, order, addOrders,ref strMsg);
                    break;
                case OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE:
                    addState = this.cPresc.AddDrug(this.PatientInfo, result, order, addOrders,ref strMsg);
                    break;
                case OrderClassDict.ORDER_CLASS_LAB:
                    addState = labTestApply.AddOrder(this.PatientInfo, order, addOrders, result, tjVsPrice);
                    break;
                case OrderClassDict.ORDER_CLASS_EXAM:
                    addState = examApply.AddOrder(this.PatientInfo, order, addOrders, result, tjVsPrice);
                    break;
                case OrderClassDict.ORDER_CLASS_TREAT:
                    addState = treatApply.AddOrder(this.PatientInfo, order, addOrders, result, tjVsPrice);
                    break;
                default:
                    addState = orderApply.AddOrder(this.PatientInfo, order, addOrders, result, tjVsPrice);
                    break;
            }
            return addState;
        }

        public bool SetCostItem(OUTP_ORDERS_STANDARD order, OUTP_ORDERS_COSTS_STANDARD addCost, InputResult result)
        {
            bool addState = true;
            TJ_VS_PRICE tjVsPrice = tjVsPriceDal.GetModelBySql(new TJ_VS_PRICE { ITEM_CODE = result.ItemCode });
            string itemClass = result.ItemClass;
            string performBy = string.IsNullOrEmpty(result.Performed_dept) ? GlobalValue.DeptCode : result.Performed_dept;//执行科室为空的默认当前登录科室
            #region 非药品 
            if (!itemClass.Equals("J"))
            {
                bool isDrug = OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(itemClass) || OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(itemClass);
                DataTable dtClinicVsCharge = priceListDal.GetClinicCharge(SystemParm.HisUnitCode, itemClass, result.ItemCode);

                if (dtClinicVsCharge == null || dtClinicVsCharge.Rows.Count == 0)
                {
                    XtraMessageBox.Show("未找到项目【" + result.ItemName + "】对应的收费项目！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
                // 根据费用明细添加新费用记录
                int itemNo = (int)addCost.ITEM_NO;
                for (int i = 0; i < dtClinicVsCharge.Rows.Count; i++)
                {
                    DataRow dr = dtClinicVsCharge.Rows[0];
                    string chgItemClass = dr["CHARGE_ITEM_CLASS"].ToString();
                    string chgItemCode = dr["CHARGE_ITEM_CODE"].ToString();
                    string chgItemSpec = dr["CHARGE_ITEM_SPEC"].ToString();
                    string chgUnits = dr["UNITS"].ToString();
                    decimal price = decimal.Zero;
                    decimal chgRelationAmount = dr["AMOUNT"].ToDecimal(1);
                    CURRENT_PRICE_LIST searchPriceListItem = new CURRENT_PRICE_LIST
                    {
                        ITEM_CODE = chgItemCode,
                        HIS_UNIT_CODE = SystemParm.HisUnitCode,
                        ITEM_CLASS = chgItemClass,
                        ITEM_SPEC = chgItemSpec,
                        UNITS = chgUnits
                    };
                    if (itemClass.Equals(OrderClassDict.ORDER_CLASS_MATERIAL))
                    {
                        searchPriceListItem.ITEM_CLASS = "I";
                    }
                    if (itemClass.Equals("Z"))
                    {
                        searchPriceListItem.UNITS = "";
                        searchPriceListItem.ITEM_SPEC = "";
                    }
                    CURRENT_PRICE_LIST priceListItem = priceListDal.GetSingle(searchPriceListItem);
                    if (null == priceListItem)
                    {
                        throw new MessageException($"查询项目【{result.ItemName}】价格失败");
                    }
                    price = priceListItem.PRICE.ToDecimal(0);
                    // 计算费用金额
                    decimal chargePrice = decimal.Zero;
                    decimal defaultFactor = 1;
                    int proportion_numerator = 0;
                    int proportion_denominator = 0;
                    decimal personal_price = decimal.Zero;
                    if (PlatCommon.Comm.class_obilling_public.f_his21_calc_charge_price_outp(this.PatientInfo.CHARGE_TYPE, chgItemClass, chgItemCode, chgItemSpec, price,defaultFactor, ref chargePrice, ref proportion_numerator,ref proportion_denominator,ref personal_price) < 0)
                    {
                        throw new MessageException("计算应收价格失败");
                    }
                    //添加费用记录
                    if (i != 0)
                    {
                        addCost = new OUTP_ORDERS_COSTS_STANDARD();
                    }
                    addCost.INSUR_ADULT = order.INSUR_ADULT;
                    addCost.PATIENT_ID = this.PatientInfo.PATIENT_ID;
                    addCost.CLINIC_NO = this.PatientInfo.CLINIC_NO;
                    addCost.VISIT_DATE = order.VISIT_DATE;
                    addCost.VISIT_NO = order.VISIT_NO;
                    addCost.ORDER_CLASS = itemClass;
                    addCost.ITEM_CLASS = priceListItem.ITEM_CLASS;
                    addCost.ITEM_CODE = priceListItem.ITEM_CODE;
                    addCost.ITEM_NAME = priceListItem.ITEM_NAME;
                    addCost.SERIAL_NO = order.SERIAL_NO;
                    addCost.OUTP_SERIAL_NO = order.OUTP_SERIAL_NO;
                    addCost.ORDER_NO = order.ORDER_NO;
                    addCost.ORDER_SUB_NO = order.ORDER_SUB_NO;
                    addCost.CHARGE_INDICATOR = 0;
                    addCost.ORDERED_BY_DEPT = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE;
                    addCost.ORDERED_BY_DOCTOR = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                    addCost.PERFORMED_BY = performBy;
                    addCost.REPETITION = 1;
                    addCost.GJBM = tjVsPrice?.INSUR_CODE;
                    addCost.GJMC = tjVsPrice?.INSUR_NAME;
                    addCost.SJBM = tjVsPrice?.INSUR_CODE;
                    addCost.SJMC = tjVsPrice?.INSUR_NAME;
                    addCost.CLASS_ON_RCPT = priceListItem.CLASS_ON_OUTP_RCPT;
                    addCost.CLASS_ON_RECKONING = priceListItem.CLASS_ON_RECKONING;
                    addCost.SUBJ_CODE = priceListItem.SUBJ_CODE;
                    // 皮试标志
                    if (priceListItem.ITEM_CLASS == "A" && order.ORDER_CLASS == "A")
                    {
                        addCost.SKINTEST = order.SKIN_FLAG.ToString();
                    }

                    // 费用顺序号
                    if (i != 0)
                    {
                        addCost.ITEM_NO = itemNo;
                    }

                    // 途径数量以对照数量计算，真实价格需要根据【西药/草药】重新计算

                    // 数量、规格、单位赋值
                    addCost.ChargeAmount = 1;
                    addCost.AMOUNT = 1;
                    addCost.UNITS = chgUnits;
                    addCost.ITEM_SPEC = chgItemSpec;
                    order.PRICE = price;
                    order.CHARGE_PRICE = chargePrice;
                    //处置中包含该伪列 如果价格为0  设置标识 可编辑  2021/3/24 YYS 
                    if (chargePrice <= 0)
                    {
                        order.ModifyFlag = "0";
                    }

                    // 设置费用计价、金额
                    addCost.COSTS = decimal.Round(price * addCost.AMOUNT.ToDecimal(1), "AB".Contains(order.ORDER_CLASS) ? 4 : 2);
                    addCost.CHARGES = decimal.Round(chargePrice * addCost.AMOUNT.ToDecimal(1), "AB".Contains(order.ORDER_CLASS) ? 4 : 2);
                    addCost.ITEM_PRICE = decimal.Round(price, 6);
                    addCost.CHARGE_PRICE = decimal.Round(chargePrice, 6);
                    // 设置项目开单序号
                    addCost.HIS_UNIT_CODE = PlatCommon.SysBase.SystemParm.HisUnitCode;
                    addCost.CLINIC_NO = order.CLINIC_NO;
                    if (i != 0)
                    {
                        order.OrderCosts.Add(addCost);
                    }
                    itemNo++;
                }
            }
            else
            {
                decimal price = 0;
                decimal chargePrice = 0;
                //order.OrderCosts?.Clear();
                //order.OrderCosts = new List<OUTP_ORDERS_COSTS_STANDARD>();
                CURRENT_PRICE_LIST priceListItem = priceListDal.GetPriceListItem("I", result.ItemCode, result.ItemSpec+ result.ItemFirm, result.ItemUnit);

                if (priceListItem == null)
                {
                    throw new MessageException("未查询到【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的价表信息!");
                }
                addCost.INSUR_ADULT = order.INSUR_ADULT;
                addCost.PATIENT_ID = this.PatientInfo.PATIENT_ID;
                addCost.CLINIC_NO = this.PatientInfo.CLINIC_NO;
                addCost.VISIT_DATE = order.VISIT_DATE;
                addCost.VISIT_NO = order.VISIT_NO;
                addCost.ORDER_CLASS = "I";
                addCost.ORDER_NO = order.ORDER_NO;
                addCost.ORDER_SUB_NO = order.ORDER_SUB_NO;
                //addCost.ITEM_NO = 1;
                addCost.ITEM_NAME = priceListItem.ITEM_NAME;
                addCost.ITEM_CODE = priceListItem.ITEM_CODE;
                addCost.ITEM_SPEC = priceListItem.ITEM_SPEC;
                addCost.ITEM_CLASS = "I";
                addCost.UNITS = priceListItem.UNITS;
                addCost.REPETITION = 1;
                addCost.GJBM = tjVsPrice?.INSUR_CODE;
                addCost.GJMC = tjVsPrice?.INSUR_NAME;
                addCost.SJBM = tjVsPrice?.INSUR_CODE;
                addCost.SJMC = tjVsPrice?.INSUR_NAME;
                addCost.CLASS_ON_RCPT = priceListItem.CLASS_ON_OUTP_RCPT;
                addCost.CLASS_ON_RECKONING = priceListItem.CLASS_ON_RECKONING;
                addCost.SUBJ_CODE = priceListItem.SUBJ_CODE;
                decimal ld_charge_price = 0;
                string titem_price = priceListItem.PRICE.ToString();
                string tamount = result.ItemQuantity;
                if (string.IsNullOrEmpty(tamount))
                {
                    tamount = "1";
                }
                decimal citem_price = 0;
                if (!string.IsNullOrEmpty(titem_price))
                {
                    citem_price = Convert.ToDecimal(titem_price);
                }
                decimal camount = Math.Round(Convert.ToDecimal(tamount), 3);

                if (!priceListDal.GetClinicItemPrice(priceListItem, this.PatientInfo.CHARGE_TYPE, ref price, ref chargePrice))
                {
                    throw new MessageException("没找到" + priceListItem.ITEM_NAME + "价格！");
                }

                addCost.ITEM_PRICE = citem_price;
                addCost.CHARGE_PRICE = chargePrice;
                addCost.COSTS = citem_price * camount;
                addCost.CHARGES = chargePrice * camount;
                addCost.AMOUNT = camount;
                addCost.ChargeAmount = camount;
                addCost.ITEM_SPEC = priceListItem.ITEM_SPEC;
                addCost.UNITS = priceListItem.UNITS;
                addCost.PERFORMED_BY = performBy;
                addCost.ORDERED_BY_DEPT = order.ORDERED_BY;
                addCost.ORDERED_BY_DOCTOR = order.DOCTOR_NO;
                addCost.HIS_UNIT_CODE = SystemParm.HisUnitCode;
            }
            SetCostAmount(1, order, addCost);
            #endregion
            return addState;
        }

        private bool CheckRepeat(OUTP_ORDERS_STANDARD order, List<OUTP_ORDERS_STANDARD> addOrders, InputResult result)
        {
            if (addOrders.Where(r => r.ORDER_CLASS.ToString("") == result.ItemClass && r.ORDER_CODE == result.ItemCode && !(result.ItemCode.Equals(order.ORDER_CODE))).Count() > 0)
            {
                if (result.ItemClass.Equals(OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE) || result.ItemClass.Equals(OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE))
                {
                    throw new MessageException("【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的项目已经开过,不允许重复开药!");
                }
                else
                {
                    if (XtraMessageBox.Show("【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的项目已经开过，是否继续？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        private bool saveOldOrders = false;
        protected OracleDataHelper db = new OracleDataHelper();
        private DataTable dtPrescOld;
        private DataTable dtTreatRecOld;
        private DataTable dtOrderCostsOld;
        private DataTable dtOrdersOld;
        /// <summary>
        /// 处理旧版本的数据
        /// </summary>
        /// <param name="dsSave"></param>
        /// <returns></returns>
        public DataSet SetOrderDataSet(DataSet dsSave)
        {
            if (!saveOldOrders)
            {
                return dsSave;
            }
            DataTable dtOrdersOld = GetOrdersOld();
            DataTable dtOrderCostsOld = GetOrderCostsOld();
            DataTable dtTreatRecOld = GetTreatRecOld();
            DataTable dtPrescOld = GetPrescOld();
            //处理处方
            List<DataRow> drPrescs = dsSave.Tables["OUTP_ORDERS_STANDARD"].AsEnumerable().Where(dr => dr["ORDER_CLASS"].Equals(OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE)).ToList();
            SetPrescOld(dtOrdersOld, dtPrescOld, dtOrderCostsOld, dsSave.Tables["OUTP_ORDERS_COSTS_STANDARD"], drPrescs);
            drPrescs = dsSave.Tables["OUTP_ORDERS_STANDARD"].AsEnumerable().Where(dr => dr["ORDER_CLASS"].Equals(OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE)).ToList();
            SetPrescOld(dtOrdersOld, dtPrescOld, dtOrderCostsOld, dsSave.Tables["OUTP_ORDERS_COSTS_STANDARD"], drPrescs);
            //ORDER_SUB_NO ORDER_NO
            //处理其他
            List<DataRow> drTreats = dsSave.Tables["OUTP_ORDERS_STANDARD"].AsEnumerable().Where(dr => !new List<string> { OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE, OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE }.Contains(dr["ORDER_CLASS"].ToString())).ToList();
            SetTreatOld(dtOrdersOld, dtTreatRecOld, dtOrderCostsOld, dsSave.Tables["OUTP_ORDERS_COSTS_STANDARD"], drTreats);
            dsSave.Tables.Remove("OUTP_ORDERS_STANDARD");
            dsSave.Tables.Remove("OUTP_ORDERS_COSTS_STANDARD");
            dsSave.Tables.Remove("IND_OUTP_ORDERS_COSTS");
            dsSave.Tables.Add(dtOrdersOld);
            dsSave.Tables.Add(dtOrderCostsOld);
            dsSave.Tables.Add(dtTreatRecOld);
            dsSave.Tables.Add(dtPrescOld);
            return dsSave;

        }
        private DataTable GetOrdersOld()
        {
            if (this.dtOrdersOld == null)
            {
                using (OracleDataHelper dao = new OracleDataHelper())
                {
                    string sql = "Select * from outp_orders where 1=2";
                    this.dtOrdersOld = dao.SelectDataTable(sql, null, "OUTP_ORDERS");
                }
                return this.dtOrdersOld.Clone();
            }
            return this.dtOrdersOld.Clone();
        }
        private DataTable GetOrderCostsOld()
        {
            if (this.dtOrderCostsOld == null)
            {
                using (OracleDataHelper dao = new OracleDataHelper())
                {
                    string sql = "select * from OUTP_ORDERS_COSTS where 1=2";
                    this.dtOrderCostsOld = dao.SelectDataTable(sql, null, "OUTP_ORDERS_COSTS");
                }
                return this.dtOrderCostsOld.Clone();
            }
            return this.dtOrderCostsOld.Clone();
        }

        private DataTable GetTreatRecOld()
        {
            if (this.dtTreatRecOld == null)
            {
                using (OracleDataHelper dao = new OracleDataHelper())
                {
                    string sql = "select * from OUTPDOCT.outp_treat_rec t where 1=2";
                    this.dtTreatRecOld = dao.SelectDataTable(sql, null, "OUTP_TREAT_REC");
                }
                return this.dtTreatRecOld.Clone();
            }
            return this.dtTreatRecOld.Clone();
        }

        private DataTable GetPrescOld()
        {
            if (this.dtPrescOld == null)
            {
                using (OracleDataHelper dao = new OracleDataHelper())
                {
                    string sql = "select * from OUTPDOCT.OUTP_PRESC t where 1=2";
                    this.dtPrescOld = dao.SelectDataTable(sql, null, "OUTP_PRESC");
                }
                return this.dtPrescOld.Clone();
            }
            return this.dtPrescOld.Clone();
        }
        private void SetPrescOld(DataTable dtOrdersOld, DataTable dtPresc, DataTable dtOrdersCostOld, DataTable dtOrderCosts, List<DataRow> drOrderPrescs)
        {
            if (drOrderPrescs == null || drOrderPrescs.Count == 0)
            {
                return;
            }
            int itemNo = 1;
            string prescNo = "";
            string serialNo = "";
            drOrderPrescs.ForEach(dr =>
            {
                DataRow drPresc = dtPresc.NewRow();
                drPresc["VISIT_DATE"] = dr["VISIT_DATE"];
                drPresc["VISIT_NO"] = dr["VISIT_NO"];
                drPresc["SERIAL_NO"] = dr["SERIAL_NO"];
                drPresc["PRESC_NO"] = dr["APPOINT_NO"];
                drPresc["ITEM_NO"] = itemNo;
                drPresc["ITEM_CLASS"] = dr["ORDER_CLASS"];
                drPresc["DRUG_CODE"] = dr["ORDER_CODE"];
                drPresc["DRUG_NAME"] = dr["ORDER_TEXT"];
                drPresc["DRUG_SPEC"] = dr["ITEM_SPEC"];
                drPresc["FIRM_ID"] = dr["FIRM_ID"];
                drPresc["UNITS"] = dr["UNITS"];
                drPresc["AMOUNT"] = dr["AMOUNT"];
                drPresc["DOSAGE"] = dr["DOSAGE"];
                drPresc["DOSAGE_UNITS"] = dr["DOSAGE_UNITS"];
                drPresc["ADMINISTRATION"] = dr["ADMINISTRATION"];
                drPresc["FREQUENCY"] = dr["FREQUENCY"];
                drPresc["PROVIDED_INDICATOR"] = 1;//没有
                drPresc["COSTS"] = dr["COSTS"];
                drPresc["CHARGES"] = dr["CHARGES"];
                drPresc["CHARGE_INDICATOR"] = dr["CHARGE_INDICATOR"];
                drPresc["DISPENSARY"] = dr["PERFORMED_BY"];
                drPresc["REPETITION"] = dr["REPETITION"];
                drPresc["ORDER_NO"] = dr["ORDER_NO"];
                drPresc["SUB_ORDER_NO"] = dr["ORDER_SUB_NO"];
                drPresc["FREQ_DETAIL"] = dr["FREQ_DETAIL"];
                drPresc["GETDRUG_FLAG"] = dr["GETDRUG_FLAG"];
                drPresc["PRESC_ATTR"] = dr["PRESC_ATTR"];
                drPresc["ABIDANCE"] = dr["ABIDANCE"];
                //滕州客户化的，先不处理 开始
                //drPresc["PERFORM_NURSE"] = dr[""];
                //drPresc["PERFORM_RESULT"] = dr[""];
                //drPresc["SIGN_NAME"] = dr[""];
                //drPresc["DOUBLE_SIGN"] = dr[""];
                //结束
                drPresc["SKIN_FLAG"] = dr["SKIN_FLAG"];
                drPresc["PRESC_PSNO"] = dr["PRESC_PSNO"];
                drPresc["SKIN_RESULT"] = dr["SKIN_RESULT"];
                drPresc["PERFORM_TIMES"] = dr["PERFORM_TIMES"];
                drPresc["RCPT_NO"] = dr["RCPT_NO"];
                //drPresc["CLINIC_SERIAL_NO"] = dr["CLINIC_NO"].ToString() + dr["SERIAL_NO"].ToString();
                //当前的处方号不为空并且不等于前一个处方号时，itemNo重新从1开始，每个处方的item_no从1开始
                if (!string.IsNullOrEmpty(prescNo) && !prescNo.Equals(dr["APPOINT_NO"].ToString()))
                {
                    itemNo = 1;
                }
                else
                {
                    itemNo++;
                }
                if (string.IsNullOrEmpty(serialNo) || !serialNo.Equals(dr["SERIAL_NO"].ToString()))
                {
                    SetOrders(dtOrdersOld, drOrderPrescs[0]);
                }
                serialNo = dr["SERIAL_NO"].ToString();
                prescNo = dr["APPOINT_NO"].ToString();
                List<DataRow> drCosts = dtOrderCosts.AsEnumerable().Where(drCostsWhere =>
                drCostsWhere["ORDER_NO"].ToString().Equals(dr["ORDER_NO"].ToString()) &&
                drCostsWhere["ORDER_SUB_NO"].ToString().Equals(dr["ORDER_SUB_NO"].ToString()) &&
                drCostsWhere["SERIAL_NO"].ToString().Equals(dr["SERIAL_NO"].ToString())).
                ToList();
                SetOrderCostOld(dtOrdersCostOld, drCosts);
                dtPresc.Rows.Add(drPresc);
            });
        }
        private void SetTreatOld(DataTable dtOrdersOld, DataTable dtTreatRecOld, DataTable dtOrderCostsOld, DataTable dtOrderCosts, List<DataRow> drTreats)
        {
            if (drTreats == null || drTreats.Count == 0)
            {
                return;
            }
            int itemNo = 1;
            int appointItemNo = 1;
            string appointNo = "";
            string serialNo = "";
            string orderClass = "";
            drTreats.ForEach(dr =>
            {
                orderClass = dr["ORDER_CLASS"].ToString();
                DataRow drTreatOld = dtTreatRecOld.NewRow();
                drTreatOld["VISIT_DATE"] = dr["VISIT_DATE"];
                drTreatOld["VISIT_NO"] = dr["VISIT_NO"];
                drTreatOld["SERIAL_NO"] = dr["SERIAL_NO"];
                if (string.IsNullOrEmpty(serialNo) || !serialNo.Equals(dr["SERIAL_NO"].ToString()))
                {
                    SetOrders(dtOrdersOld, dr);

                }
                else
                {
                    //if (!serialNo.Equals(dr["SERIAL_NO"].ToString()) && !string.IsNullOrEmpty(serialNo)  )
                    //{
                    //    itemNo = 1;
                    //}
                    //else
                    //{
                    //    itemNo++;
                    //}
                }
                drTreatOld["ITEM_NO"] = dr["ORDER_NO"];// itemNo;
                serialNo = dr["SERIAL_NO"].ToString();
                drTreatOld["ITEM_CLASS"] = dr["ORDER_CLASS"];
                drTreatOld["ITEM_CODE"] = dr["ORDER_CODE"];
                drTreatOld["ITEM_NAME"] = dr["ORDER_TEXT"];
                drTreatOld["ITEM_SPEC"] = dr["ITEM_SPEC"];
                drTreatOld["UNITS"] = dr["UNITS"];
                drTreatOld["AMOUNT"] = dr["AMOUNT"];
                drTreatOld["FREQUENCY"] = dr["FREQUENCY"];
                drTreatOld["PERFORMED_BY"] = dr["PERFORMED_BY"];
                drTreatOld["COSTS"] = dr["COSTS"];
                drTreatOld["CHARGES"] = dr["CHARGES"];
                drTreatOld["CHARGE_INDICATOR"] = dr["CHARGE_INDICATOR"];
                drTreatOld["APPOINT_NO"] = dr["APPOINT_NO"];
                drTreatOld["APPOINT_ITEM_NO"] = appointItemNo;
                drTreatOld["SUB_ORDER_NO"] = dr["ORDER_SUB_NO"];
                drTreatOld["ORDER_NO"] = dr["ORDER_NO"];
                if (!string.IsNullOrEmpty(appointNo) && !appointNo.Equals(dr["APPOINT_NO"].ToString()))
                {
                    appointItemNo = 1;
                }
                else
                {
                    appointItemNo++;
                }
                appointNo = dr["APPOINT_NO"].ToString();
                drTreatOld["FREQ_DETAIL"] = dr["FREQ_DETAIL"];
                drTreatOld["RCPT_NO"] = dr["RCPT_NO"];
                drTreatOld["SKIN_RESULT"] = dr["SKIN_RESULT"];
                //drTreatOld["SKIN_DATE"] = dr["SKIN_DATE"];
                //drTreatOld["SKIN_OPERATOR"] = dr["SKIN_OPERATOR"];
                //drTreatOld["SKIN_INPUT"] = dr["SKIN_INPUT"];
                //drTreatOld["CHECKER"] = dr["CHECKER"];
                //drTreatOld["CHECK_DATE"] = dr["CHECK_DATE"];
                //drTreatOld["INSURANCE_FLAG"] = dr["INSURANCE_FLAG"];
                List<DataRow> drCosts = dtOrderCosts.AsEnumerable().Where(drCostsWhere =>
                drCostsWhere["ORDER_NO"].ToString().Equals(dr["ORDER_NO"].ToString()) &&
                drCostsWhere["ORDER_SUB_NO"].ToString().Equals(dr["ORDER_SUB_NO"].ToString()) &&
                drCostsWhere["SERIAL_NO"].ToString().Equals(dr["SERIAL_NO"].ToString())).ToList();
                SetOrderCostOld(dtOrderCostsOld, drCosts);
                dtTreatRecOld.Rows.Add(drTreatOld);
            });
        }
        private void SetOrders(DataTable dtOrdersOld, DataRow drOrders)
        {
            DataRow drOrdersOld = dtOrdersOld.NewRow();
            drOrdersOld["PATIENT_ID"] = drOrders["PATIENT_ID"];
            drOrdersOld["VISIT_DATE"] = drOrders["VISIT_DATE"];
            drOrdersOld["VISIT_NO"] = drOrders["VISIT_NO"];
            drOrdersOld["SERIAL_NO"] = drOrders["SERIAL_NO"];
            drOrdersOld["ORDERED_BY"] = drOrders["ORDERED_BY"];
            drOrdersOld["DOCTOR"] = drOrders["DOCTOR"];
            drOrdersOld["CLINIC_NO"] = drOrders["CLINIC_NO"];
            drOrdersOld["DOCTOR_NO"] = drOrders["DOCTOR_NO"];
            drOrdersOld["ORDER_DATE"] = drOrders["ORDER_DATE"];
            //drOrdersOld["NURSE"] = drOrders[""];
            //drOrdersOld["DIAGNOSIS_DESC"] = drOrders[""];
            dtOrdersOld.Rows.Add(drOrdersOld);
        }
        private void SetOrderCostOld(DataTable dtOrderCostsOld, List<DataRow> drCostsStandard)
        {
            if (drCostsStandard == null || drCostsStandard.Count == 0)
            {
                return;
            }

            string orderClass = "";
            drCostsStandard.ForEach(dr =>
            {
                DataRow drCosts = dtOrderCostsOld.NewRow();
                //赋值
                drCosts["PATIENT_ID"] = dr["PATIENT_ID"];
                drCosts["VISIT_DATE"] = dr["VISIT_DATE"];
                drCosts["VISIT_NO"] = dr["VISIT_NO"];
                drCosts["SERIAL_NO"] = dr["SERIAL_NO"];
                drCosts["ORDER_CLASS"] = dr["ORDER_CLASS"];
                orderClass = dr["ITEM_CLASS"].ToString();
                //if (OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(orderClass) || OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(orderClass))
                //{

                //    drCosts["ORDER_NO"] = dr["ORDER_NO"];
                //    drCosts["ORDER_SUB_NO"] = dr["ORDER_SUB_NO"];
                //}
                //else
                //{
                //    drCosts["ORDER_NO"] = dr["ITEM_NO"];
                //    drCosts["ORDER_SUB_NO"] = dr["ORDER_SUB_NO"];
                //}

                drCosts["ORDER_NO"] = dr["ORDER_NO"];
                drCosts["ORDER_SUB_NO"] = dr["ORDER_SUB_NO"];
                //if (OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(orderClass) || OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(orderClass))
                //{
                //    drCosts["ITEM_NO"] = dr["ITEM_NO"];
                //}
                //else
                //{
                //    drCosts["ITEM_NO"] = dr["ORDER_NO"];
                //}
                drCosts["ITEM_NO"] = dr["ITEM_NO"];
                drCosts["ITEM_CLASS"] = dr["ITEM_CLASS"];
                drCosts["ITEM_NAME"] = dr["ITEM_NAME"];
                drCosts["ITEM_CODE"] = dr["ITEM_CODE"];
                drCosts["ITEM_SPEC"] = dr["ITEM_SPEC"];
                drCosts["UNITS"] = dr["UNITS"];
                drCosts["REPETITION"] = dr["REPETITION"];
                drCosts["AMOUNT"] = dr["AMOUNT"];
                drCosts["ORDERED_BY_DEPT"] = dr["ORDERED_BY_DEPT"];
                drCosts["ORDERED_BY_DOCTOR"] = dr["ORDERED_BY_DOCTOR"];
                drCosts["PERFORMED_BY"] = dr["PERFORMED_BY"];
                drCosts["CLASS_ON_RCPT"] = dr["CLASS_ON_RCPT"];
                drCosts["COSTS"] = dr["COSTS"];
                drCosts["CHARGES"] = dr["CHARGES"];
                drCosts["RCPT_NO"] = dr["RCPT_NO"];
                //drCosts["BILL_DESC_NO"] = dr["BILL_DESC_NO"];
                //drCosts["BILL_ITEM_NO"] = dr["BILL_ITEM_NO"];
                drCosts["CHARGE_INDICATOR"] = dr["CHARGE_INDICATOR"];
                drCosts["CLASS_ON_RECKONING"] = dr["CLASS_ON_RECKONING"];
                drCosts["SUBJ_CODE"] = dr["SUBJ_CODE"];
                drCosts["PRICE_QUOTIETY"] = dr["PRICE_QUOTIETY"];
                drCosts["ITEM_PRICE"] = dr["ITEM_PRICE"];
                drCosts["CLINIC_NO"] = dr["CLINIC_NO"];
                //drCosts["BILL_DATE"] = dr[""];
                //drCosts["BILL_NO"] = dr[""];
                //drCosts["YSQM"] = dr[""];
                dtOrderCostsOld.Rows.Add(drCosts);
            });
        }

        public bool IsShowConfirmDialog { get; set; }
        #region 释放
        private bool isDisposed;
        public bool IsDisposed
        {
            get { return isDisposed; }
            set { isDisposed = value; }
        }


        public void Dispose()
        {
            if (!this.isDisposed)
            {
                this.Dispose(true);
                GC.SuppressFinalize(this);
                this.isDisposed = true;
            }
        }
        public virtual void Dispose(bool disposed)
        {
            if (disposed)
            {
                if (null != db)
                {
                    db.Dispose();
                    db = null;
                }
                labTestApply?.Dispose();
                examApply?.Dispose();
                treatApply?.Dispose();
                AddOrders?.Clear();
                DeptDictDal?.Dispose();
            }
        }
        ~OrderBusiness()
        {
            Dispose(false);
        }
        #endregion

        public bool CanShowEditor(OUTP_ORDERS_STANDARD order, List<OUTP_ORDERS_STANDARD> addOrders, string foucsFieldName)
        {
            if (string.IsNullOrEmpty(foucsFieldName))
            {
                return true;
            }
            if (foucsFieldName.Equals("ISCHECK"))
            {
                return true;
            }
            if (!order.STATE.Equals(Constants.NEW_ORDER_STATE_STR))
            {
                return false;
            }
            if (foucsFieldName.Equals("ORDER_TEXT") && string.IsNullOrEmpty(order.ORDER_TEXT))
            {
                return true;
            }
            if (foucsFieldName.Equals("ORDER_CLASS") && !string.IsNullOrEmpty(order.ORDER_TEXT))
            {
                return false;
            }
            string orderClass = order.ORDER_CLASS;
            List<string> cannotShowEditorFiledNames;
            switch (orderClass)
            {
                case OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE:
                    //判断是否为组医嘱中的

                    cannotShowEditorFiledNames = new List<string>
                    {
                        "PERFORMED_BY",
                        "EXAM_CLASS",
                        "SKIN_RESULT"
                    };
                    if (order.ORDER_SUB_NO > 1)
                    {
                        cannotShowEditorFiledNames.Add("ADMINISTRATION");
                        cannotShowEditorFiledNames.Add("FREQUENCY");
                    }
                    if (isCansShowEditor(foucsFieldName, cannotShowEditorFiledNames))
                    {
                        return false;
                    }
                    break;
                case OrderClassDict.ORDER_CLASS_LAB:
                    cannotShowEditorFiledNames = new List<string>
                    {
                        "ADMINISTRATION","FREQUENCY","DOSAGE","ABIDANCE","AMOUNT","SKIN_FLAG","SKIN_RESULT"
                    };
                    if (isCansShowEditor(foucsFieldName, cannotShowEditorFiledNames))
                    {
                        return false;
                    }
                    break;
                case OrderClassDict.ORDER_CLASS_EXAM:
                    cannotShowEditorFiledNames = new List<string>
                    {
                        "ADMINISTRATION","FREQUENCY","DOSAGE","ABIDANCE","EXAM_CLASS","PERFORMED_BY","SKIN_FLAG","SKIN_RESULT"
                    };
                    if (isCansShowEditor(foucsFieldName, cannotShowEditorFiledNames))
                    {
                        return false;
                    }
                    break;
                default:
                    cannotShowEditorFiledNames = new List<string>
                    {
                        //"ADMINISTRATION","FREQUENCY","DOSAGE","ABIDANCE","EXAM_CLASS","SKIN_FLAG","SKIN_RESULT"
                        "FREQUENCY","ABIDANCE","EXAM_CLASS","SKIN_FLAG","SKIN_RESULT"
                    };
                    if (isCansShowEditor(foucsFieldName, cannotShowEditorFiledNames))
                    {
                        return false;
                    }
                    break;

            }
            return true;
        }

        private bool isCansShowEditor(string foucsFieldName, List<string> cannotShowEditorFiledNames)
        {
            return cannotShowEditorFiledNames.Contains(foucsFieldName);
        }

        public void Clear(OUTP_ORDERS_STANDARD order)
        {
            order.ORDER_CODE = "";
            order.ORDER_CLASS = "";
            order.ORDER_TEXT = "";
            order.ADMINISTRATION = "";
            order.FREQUENCY = "";
            order.UNITS = "";
            order.ITEM_SPEC = "";
            order.IsApplyItem = false;
            order.MIN_SPEC = "";
            order.ADMINISTRATION = "";
            order.FREQUENCY = "";
            order.FREQ_DETAIL = "";
            order.FIRM_ID = "";
            order.PRESC_ATTR = "";
            order.SKIN_FLAG = null;

            order.PRICE = 0;
            order.CHARGE_PRICE = 0;
            order.COSTS = 0;
            order.CHARGES = 0;
            order.HIGH_DANGER = "";

            order.DOSAGE_UNITS = "";
            order.OFFICIAL_CATALOG = "";
            order.TOXI_PROPERTY = "";
            order.DRUG_INDICATOR = 0;
            order.DOSE_PER_UNIT = null;
            order.AMOUNT_PER_PACKAGE = 0;
            order.PERFORMED_BY = "";
            order.USABLE_QUANTITY = 0;
            order.BATCH_NO = "";//个人感觉批次应该在发药的时候再处理，不应该在加药的时候就选择，按我的理解，药品应该是先进先出，不应该是人为的控制开药的批次
            order.RECIPETYPE = "";//军队医改2022
            order.DOSAGE = 0;

        }
        /// <summary>
        /// 按选则的项目设置新方
        /// </summary>
        /// <param name="orders"></param>
        /// <returns></returns>
        public bool SetNewPresc(List<OUTP_ORDERS_STANDARD> orders)
        {
            return true;
        }

        public OutpPatientInfo GetCurrentPatient()
        {
            return this.PatientInfo;
        }

        public List<OUTP_ORDERS_STANDARD> Add(List<InputResult> inputResults, List<OUTP_ORDERS_STANDARD> addOrders)
        {
            List<OUTP_ORDERS_STANDARD> orders = new List<OUTP_ORDERS_STANDARD>();
            string strMsg = "";

            // 修复：预先计算ORDER_NO分配，避免主键冲突
            Dictionary<int, int> orderNoMapping = new Dictionary<int, int>(); // SubOrderNo -> ORDER_NO
            int currentOrderNo = this.MaxOrderNo;

            // 第一遍：为每个不同的SubOrderNo分配唯一的ORDER_NO
            foreach (var addItem in inputResults)
            {
                if (OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(addItem.ItemClass))
                {
                    if (!orderNoMapping.ContainsKey(addItem.SubOrderNo))
                    {
                        if (addItem.SubOrderNo == 1)
                        {
                            currentOrderNo++;
                            orderNoMapping[addItem.SubOrderNo] = currentOrderNo;
                        }
                        else
                        {
                            // 子处方使用与主处方相同的ORDER_NO
                            if (orderNoMapping.ContainsKey(1))
                            {
                                orderNoMapping[addItem.SubOrderNo] = orderNoMapping[1];
                            }
                            else
                            {
                                currentOrderNo++;
                                orderNoMapping[addItem.SubOrderNo] = currentOrderNo;
                            }
                        }
                    }
                }
                else
                {
                    // 非西药，每个都分配新的ORDER_NO
                    currentOrderNo++;
                }
            }

            // 更新MaxOrderNo
            this.MaxOrderNo = currentOrderNo;

            // 第二遍：创建医嘱记录
            int nonDrugOrderNo = currentOrderNo; // 用于非西药的ORDER_NO分配
            inputResults.ForEach(addItem =>
            {
                OUTP_ORDERS_STANDARD order = this.New();
                order.ITEM_NO = GetNextItemNo();
                order.OrderCosts = new List<OUTP_ORDERS_COSTS_STANDARD>();

                if (OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(addItem.ItemClass))//如果是药品要特殊处理子处方
                {
                    order.ORDER_NO = orderNoMapping[addItem.SubOrderNo];
                    order.ORDER_SUB_NO = addItem.SubOrderNo;
                }
                else
                {
                    // 非西药，分配新的ORDER_NO
                    order.ORDER_NO = ++nonDrugOrderNo;
                    order.ORDER_SUB_NO = 1;
                }

                if (this.SetClinciItem(order, addOrders, addItem,ref strMsg))
                {
                    orders.Add(order);
                    addOrders.Add(order);
                    this.AddOrders.Add(order);
                }
                else
                {
                    if(!string.IsNullOrEmpty(strMsg))
                    {
                        throw new MessageException(strMsg);
                    }
                }
            });

            // 更新MaxOrderNo为实际使用的最大值
            this.MaxOrderNo = nonDrugOrderNo;
            this.wPresc.SetSubPrescFlag(orders);
            return orders;
        }

        public OutpMr GetMrInfo()
        {
            if (this.MrInfo == null)
            {
                throw new MessageException("请先保存病历");
            }
            return this.MrInfo;
        }

        public int getMaxOrderNo()
        {
            return this.MaxOrderNo;
        }

        // 用于缓存当前会话的最大ITEM_NO，避免批量操作时重复查询数据库
        private decimal? _cachedMaxItemNo = null;

        #region 日志功能
        private static readonly string LogDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "LOG", "exLOG");
        private static readonly string LogFileName = "OrderDebug_{0}.log";
        private static readonly object lockObj = new object();

        static OrderBusiness()
        {
            // 确保日志目录存在
            if (!Directory.Exists(LogDirectory))
            {
                Directory.CreateDirectory(LogDirectory);
            }
        }

        private static void WriteLog(string message, string level = "INFO")
        {
            try
            {
                lock (lockObj)
                {
                    string logFile = Path.Combine(LogDirectory, string.Format(LogFileName, DateTime.Now.ToString("yyyyMMdd")));
                    string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{level}] {message}";

                    File.AppendAllText(logFile, logEntry + Environment.NewLine);
                }
            }
            catch (Exception ex)
            {
                // 日志写入失败时不抛出异常，避免影响主业务
                System.Diagnostics.Debug.WriteLine($"日志写入失败: {ex.Message}");
            }
        }

        private static void LogOrderNoAssignment(string method, OUTP_ORDERS_STANDARD order, string action = "")
        {
            string message = $"[{method}] {action} - " +
                           $"CLINIC_NO: {order.CLINIC_NO}, " +
                           $"ORDER_NO: {order.ORDER_NO}, " +
                           $"ORDER_SUB_NO: {order.ORDER_SUB_NO}, " +
                           $"ITEM_NO: {order.ITEM_NO}, " +
                           $"ORDER_TEXT: {order.ORDER_TEXT}, " +
                           $"ORDER_CLASS: {order.ORDER_CLASS}";
            WriteLog(message, "ORDER");
        }

        private static void LogOrdersList(string method, List<OUTP_ORDERS_STANDARD> orders, string action = "")
        {
            WriteLog($"[{method}] {action} - 医嘱数量: {orders?.Count ?? 0}", "BATCH");

            if (orders != null)
            {
                for (int i = 0; i < orders.Count; i++)
                {
                    var order = orders[i];
                    string message = $"[{method}] {action}[{i + 1}] - " +
                                   $"CLINIC_NO: {order.CLINIC_NO}, " +
                                   $"ORDER_NO: {order.ORDER_NO}, " +
                                   $"ORDER_SUB_NO: {order.ORDER_SUB_NO}, " +
                                   $"ITEM_NO: {order.ITEM_NO}, " +
                                   $"ORDER_TEXT: {order.ORDER_TEXT}, " +
                                   $"ORDER_CLASS: {order.ORDER_CLASS}, " +
                                   $"STATE: {order.STATE}";
                    WriteLog(message, "BATCH");
                }
            }
        }

        private static void LogMaxOrderNoChange(string method, int oldValue, int newValue, string action = "")
        {
            string message = $"[{method}] {action} - MaxOrderNo变化: {oldValue} -> {newValue}";
            WriteLog(message, "MAXNO");
        }

        private static void LogError(string method, string error, List<OUTP_ORDERS_STANDARD> orders = null)
        {
            WriteLog($"[{method}] 错误: {error}", "ERROR");

            if (orders != null && orders.Count > 0)
            {
                WriteLog($"[{method}] 错误时的医嘱信息:", "ERROR");
                LogOrdersList(method, orders, "错误时状态");
            }
        }

        private static void LogCheckpoint(string method, string checkpoint, string data = "")
        {
            string message = $"[{method}] 检查点[{checkpoint}] - {data}";
            WriteLog(message, "CHECKPOINT");
        }
        #endregion

        #region 主键冲突检测和修复
        /// <summary>
        /// 保存前进行主键冲突预检查
        /// </summary>
        /// <param name="orders">要保存的医嘱列表</param>
        /// <returns>true表示无冲突，false表示存在冲突</returns>
        private bool PreCheckPrimaryKeyConflicts(List<OUTP_ORDERS_STANDARD> orders)
        {
            try
            {
                LogCheckpoint("OrderBusiness.PreCheckPrimaryKeyConflicts", "开始检查", $"医嘱数量: {orders.Count}");

                // 1. 检查内存中的医嘱是否有重复的主键
                var duplicateKeys = orders
                    .GroupBy(o => new { o.CLINIC_NO, o.ORDER_NO, o.ORDER_SUB_NO })
                    .Where(g => g.Count() > 1)
                    .ToList();

                if (duplicateKeys.Any())
                {
                    foreach (var duplicate in duplicateKeys)
                    {
                        LogError("OrderBusiness.PreCheckPrimaryKeyConflicts",
                            $"内存中发现重复主键: CLINIC_NO={duplicate.Key.CLINIC_NO}, ORDER_NO={duplicate.Key.ORDER_NO}, ORDER_SUB_NO={duplicate.Key.ORDER_SUB_NO}");
                    }
                    return false;
                }

                // 2. 检查数据库中是否已存在相同主键的记录（仅对新增医嘱）
                var newOrders = orders.Where(o => Constants.NEW_ORDER_STATE_STR.Equals(o.STATE)).ToList();
                if (newOrders.Any())
                {
                    foreach (var order in newOrders)
                    {
                        if (CheckPrimaryKeyExistsInDatabase(order))
                        {
                            LogError("OrderBusiness.PreCheckPrimaryKeyConflicts",
                                $"数据库中已存在主键: CLINIC_NO={order.CLINIC_NO}, ORDER_NO={order.ORDER_NO}, ORDER_SUB_NO={order.ORDER_SUB_NO}, ORDER_TEXT={order.ORDER_TEXT}");

                            // 尝试自动修复：重新分配ORDER_NO
                            if (AutoFixPrimaryKeyConflict(order, orders))
                            {
                                LogCheckpoint("OrderBusiness.PreCheckPrimaryKeyConflicts", "自动修复成功",
                                    $"新ORDER_NO={order.ORDER_NO}, ORDER_TEXT={order.ORDER_TEXT}");
                            }
                            else
                            {
                                return false;
                            }
                        }
                    }
                }

                LogCheckpoint("OrderBusiness.PreCheckPrimaryKeyConflicts", "检查完成", "无冲突");
                return true;
            }
            catch (Exception ex)
            {
                LogError("OrderBusiness.PreCheckPrimaryKeyConflicts", $"检查过程异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查数据库中是否已存在指定主键的记录
        /// </summary>
        /// <param name="order">要检查的医嘱</param>
        /// <returns>true表示已存在，false表示不存在</returns>
        private bool CheckPrimaryKeyExistsInDatabase(OUTP_ORDERS_STANDARD order)
        {
            try
            {
                string sql = @"SELECT COUNT(*) FROM OUTPDOCT.OUTP_ORDERS_STANDARD
                              WHERE CLINIC_NO = :clinic_no
                              AND ORDER_NO = :order_no
                              AND ORDER_SUB_NO = :order_sub_no";

                List<DbParameter> parameters = new List<DbParameter>
                {
                    db.CreateDbParameter(":clinic_no", order.CLINIC_NO),
                    db.CreateDbParameter(":order_no", order.ORDER_NO),
                    db.CreateDbParameter(":order_sub_no", order.ORDER_SUB_NO)
                };

                object result = db.GetSingleValue(sql, parameters);
                int count = Convert.ToInt32(result ?? 0);

                LogCheckpoint("OrderBusiness.CheckPrimaryKeyExistsInDatabase", "数据库检查",
                    $"CLINIC_NO={order.CLINIC_NO}, ORDER_NO={order.ORDER_NO}, ORDER_SUB_NO={order.ORDER_SUB_NO}, 存在记录数={count}");

                return count > 0;
            }
            catch (Exception ex)
            {
                LogError("OrderBusiness.CheckPrimaryKeyExistsInDatabase", $"数据库检查异常: {ex.Message}");
                return true; // 异常时保守处理，认为存在冲突
            }
        }

        /// <summary>
        /// 自动修复主键冲突：重新分配ORDER_NO
        /// </summary>
        /// <param name="conflictOrder">冲突的医嘱</param>
        /// <param name="allOrders">所有医嘱列表</param>
        /// <returns>true表示修复成功，false表示修复失败</returns>
        private bool AutoFixPrimaryKeyConflict(OUTP_ORDERS_STANDARD conflictOrder, List<OUTP_ORDERS_STANDARD> allOrders)
        {
            try
            {
                int oldOrderNo = conflictOrder.ORDER_NO;

                // 获取当前最大的ORDER_NO
                int maxOrderNoInMemory = allOrders.Max(o => (int)o.ORDER_NO);
                int maxOrderNoInDb = GetMaxOrderNoFromDatabase(conflictOrder.CLINIC_NO);
                int newOrderNo = Math.Max(maxOrderNoInMemory, maxOrderNoInDb) + 1;

                // 确保新的ORDER_NO不会与其他医嘱冲突
                while (allOrders.Any(o => o != conflictOrder && o.ORDER_NO == newOrderNo && o.ORDER_SUB_NO == conflictOrder.ORDER_SUB_NO) ||
                       CheckPrimaryKeyExistsInDatabase(new OUTP_ORDERS_STANDARD
                       {
                           CLINIC_NO = conflictOrder.CLINIC_NO,
                           ORDER_NO = newOrderNo,
                           ORDER_SUB_NO = conflictOrder.ORDER_SUB_NO
                       }))
                {
                    newOrderNo++;
                }

                // 更新ORDER_NO
                conflictOrder.ORDER_NO = newOrderNo;

                // 同时更新相关的费用记录
                conflictOrder.OrderCosts?.ForEach(cost => cost.ORDER_NO = newOrderNo);

                // 更新MaxOrderNo
                this.MaxOrderNo = Math.Max(this.MaxOrderNo, newOrderNo);

                LogCheckpoint("OrderBusiness.AutoFixPrimaryKeyConflict", "修复成功",
                    $"ORDER_TEXT={conflictOrder.ORDER_TEXT}, 原ORDER_NO={oldOrderNo}, 新ORDER_NO={newOrderNo}");

                return true;
            }
            catch (Exception ex)
            {
                LogError("OrderBusiness.AutoFixPrimaryKeyConflict", $"自动修复失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从数据库获取指定诊疗号的最大ORDER_NO
        /// </summary>
        /// <param name="clinicNo">诊疗号</param>
        /// <returns>最大ORDER_NO</returns>
        private int GetMaxOrderNoFromDatabase(string clinicNo)
        {
            try
            {
                string sql = @"SELECT NVL(MAX(ORDER_NO), 0) FROM OUTPDOCT.OUTP_ORDERS_STANDARD
                              WHERE CLINIC_NO = :clinic_no";

                List<DbParameter> parameters = new List<DbParameter>
                {
                    db.CreateDbParameter(":clinic_no", clinicNo)
                };

                object result = db.GetSingleValue(sql, parameters);
                return Convert.ToInt32(result ?? 0);
            }
            catch (Exception ex)
            {
                LogError("OrderBusiness.GetMaxOrderNoFromDatabase", $"获取最大ORDER_NO失败: {ex.Message}");
                return 0;
            }
        }
        #endregion

        /// <summary>
        /// 获取下一个可用的ITEM_NO，确保唯一性
        /// </summary>
        /// <returns></returns>
        private decimal GetNextItemNo()
        {
            try
            {
                // 如果缓存为空，从数据库获取当前患者的最大ITEM_NO
                if (_cachedMaxItemNo == null)
                {
                    string strSql = @"SELECT NVL(MAX(ITEM_NO), 0) FROM OUTPDOCT.OUTP_ORDERS_STANDARD
                                     WHERE PATIENT_ID = :t1 AND CLINIC_NO = :t2";

                    List<DbParameter> listPara = new List<DbParameter>();
                    listPara.Add(db.CreateDbParameter(":t1", this.PatientInfo.PATIENT_ID));
                    listPara.Add(db.CreateDbParameter(":t2", this.PatientInfo.CLINIC_NO));

                    decimal dbMaxItemNo = Convert.ToDecimal(db.GetSingleValue(strSql, listPara) ?? 0);

                    // 检查当前内存中已添加的医嘱的最大ITEM_NO
                    decimal memoryMaxItemNo = 0;
                    if (this.AddOrders != null && this.AddOrders.Count > 0)
                    {
                        memoryMaxItemNo = this.AddOrders.Max(o => o.ITEM_NO);
                    }

                    _cachedMaxItemNo = Math.Max(dbMaxItemNo, memoryMaxItemNo);
                }

                // 递增并返回下一个ITEM_NO
                _cachedMaxItemNo++;
                return _cachedMaxItemNo.Value;
            }
            catch (Exception ex)
            {
                // 如果获取失败，使用时间戳作为备用方案
                return DateTime.Now.Ticks % 1000000;
            }
        }

        /// <summary>
        /// 重置ITEM_NO缓存，在保存成功后调用
        /// </summary>
        private void ResetItemNoCache()
        {
            _cachedMaxItemNo = null;
        }

        public void Remove(OUTP_ORDERS_STANDARD order)
        {
            // 修复：修正条件判断，应该检查order.STATE而不是order本身
            if (order != null && Constants.NEW_ORDER_STATE_STR.Equals(order.STATE))
            {
                int orderNo = order.ORDER_NO.ToInt(1);
                this.AddOrders.Remove(order);
                //整理orderNo
                this.AddOrders.
                    Where(w => w.ORDER_NO > orderNo).
                    ToList().
                    ForEach(o =>
                    {
                        int tempOrderNo = o.ORDER_NO.ToInt(1);
                        tempOrderNo = tempOrderNo - 1;
                        o.ORDER_NO = tempOrderNo;
                        o.OrderCosts.ForEach(cost =>
                        {
                            cost.ORDER_NO = tempOrderNo;
                        });
                    });
                this.SetMaxOrderNo(AddOrders);
            }
        }

        public List<OUTP_ORDERS_STANDARD> OrderCopy(List<OUTP_ORDERS_STANDARD> selectOrders)
        {
            string strMsg = "" ;
            List<OUTP_ORDERS_STANDARD> result = new List<OUTP_ORDERS_STANDARD>();
            foreach (OUTP_ORDERS_STANDARD item in selectOrders)
            {
                OUTP_ORDERS_STANDARD order = this.Add();
                InputResult input = this.GetInputResult(item);
                if (OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(input.ItemClass))//如果是药品要特殊处理子处方
                {
                    order.ORDER_SUB_NO = input.SubOrderNo;
                    if (input.SubOrderNo > 1)
                    {
                        order.ORDER_NO = order.ORDER_NO - 1;
                        this.MaxOrderNo--;
                    }
                }
                // 修复：使用当前有效的医嘱列表进行重复检查，而不是全部的AddOrders
                // 只包含状态为"录入"的医嘱，排除已删除或毁方的医嘱
                List<OUTP_ORDERS_STANDARD> currentValidOrders = AddOrders.Where(o => Constants.NEW_ORDER_STATE_STR.Equals(o.STATE)).ToList();
                if(!SetClinciItem(order, currentValidOrders, input,ref strMsg))
                {
                    throw new MessageException(strMsg);
                }
                result.Add(order);
            }
            return result;
        }

        public InputResult GetInputResult(OUTP_ORDERS_STANDARD order)
        {
            InputResult inputResult = new InputResult();
            inputResult.ItemName = order.ORDER_TEXT;
            inputResult.ItemCode = order.ORDER_CODE;
            inputResult.ItemClass = order.ORDER_CLASS;
            inputResult.ItemFirm = order.FIRM_ID;
            inputResult.ItemUnit = order.DOSAGE_UNITS;
            inputResult.ItemSpec = order.ITEM_SPEC;// + ((OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE.Equals(order.ORDER_CLASS) || OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(order.ORDER_CLASS)) ? order.FIRM_ID:"");
            inputResult.ItemPackageSpec = order.ITEM_SPEC;
            inputResult.ItemPackageUnits = order.UNITS;
            inputResult.Administration = order.ADMINISTRATION;
            inputResult.Frequency = order.FREQUENCY;



            // 处理历史医嘱复制时 performedBy 为空的情况
            inputResult.Performed_dept = order.PERFORMED_BY;
            if (string.IsNullOrEmpty(inputResult.Performed_dept))
            {
                // 历史医嘱的 PERFORMED_BY 为空时，需要根据药品类型设置默认药局
                // 确保能从库存表正确获取价格
                if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE)
                {
                    // 西药：尝试获取默认西药房
                    string prescDrugDisps = SystemParm.GetParameterValue("PRESC_DRUG_DISPS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                    if (!string.IsNullOrEmpty(prescDrugDisps))
                    {
                        // 取第一个配置的西药房作为默认药房，使用RemoveEmptyEntries去除空元素
                        string[] drugStorages = prescDrugDisps.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (drugStorages.Length > 0 && !string.IsNullOrWhiteSpace(drugStorages[0]))
                        {
                            inputResult.Performed_dept = drugStorages[0].Trim();
                        }
                    }
                }
                else if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE)
                {
                    // 中药：尝试获取默认中药房
                    string prescCdrugDisps = SystemParm.GetParameterValue("PRESC_CDRUG_DISPS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                    if (!string.IsNullOrEmpty(prescCdrugDisps))
                    {
                        // 取第一个配置的中药房作为默认药房，使用RemoveEmptyEntries去除空元素
                        string[] cdrugStorages = prescCdrugDisps.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (cdrugStorages.Length > 0 && !string.IsNullOrWhiteSpace(cdrugStorages[0]))
                        {
                            inputResult.Performed_dept = cdrugStorages[0].Trim();
                        }
                    }
                }

                // 如果仍然为空，使用当前登录科室作为备选
                if (string.IsNullOrEmpty(inputResult.Performed_dept))
                {
                    inputResult.Performed_dept = GlobalValue.DeptCode;
                }
            }

            inputResult.OrderNo = order.ORDER_NO.ToInt();
            inputResult.SubOrderNo = order.ORDER_SUB_NO.ToInt();
            inputResult.Performed_name = this.DeptDictDal.GetName(inputResult.Performed_dept);
            inputResult.ItemQuantity = order.AMOUNT.ToString();
            inputResult.Dose_per_unit = order.DOSAGE.ToDecimal();
            inputResult.IsApplyItem = (inputResult.ItemClass.Equals(OrderClassDict.ORDER_CLASS_LAB) || inputResult.ItemClass.Equals(OrderClassDict.ORDER_CLASS_EXAM)) ? Constants.APPLY_FLAG : "";
            inputResult.LabAddFromType = Enums.AddFromType.Orders;
            inputResult.PerformTimes = order.PERFORM_TIMES.ToDecimal(1);
            inputResult.Abidance = order.ABIDANCE.ToDecimal(1);

            inputResult.InsureCode = order.INSUR_CODE;
            inputResult.InsureName = order.INSUR_NAME;
            return inputResult;
        }

        public void SetPerformedBy(string value, OUTP_ORDERS_STANDARD order)
        {
            if (order == null)
            {
                return;
            }
            order.PERFORMED_BY = value;
            order.OrderCosts?.ForEach(costs =>
            {
                costs.PERFORMED_BY = value;
            });
        }

        public void ResetData()
        {
            this.PatientInfo = null;
            this.MrInfo = null;
            this.DtDiagnosis?.Clear();
            this.AddOrders?.Clear();
        }


    }
}
