using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PlatCommon.SysBase;
using Tjhis.Outpdoct.Station.Comm;
using Tjhis.Outpdoct.Station.Entity;

namespace Tjhis.Outpdoct.Station.Utilities
{
    /// <summary>
    /// 主键冲突诊断工具
    /// 用于诊断和监控OUTP_ORDERS_STANDARD表的主键冲突问题
    /// </summary>
    public class PrimaryKeyConflictDiagnostic
    {
        private readonly OracleDataHelper db;
        private static readonly string LogDirectory = Path.Combine(Application.StartupPath, "LOG", "exLOG");
        private static readonly object lockObj = new object();

        public PrimaryKeyConflictDiagnostic()
        {
            db = new OracleDataHelper();
            
            // 确保日志目录存在
            if (!Directory.Exists(LogDirectory))
            {
                Directory.CreateDirectory(LogDirectory);
            }
        }

        /// <summary>
        /// 执行全面的主键冲突诊断
        /// </summary>
        /// <param name="clinicNo">诊疗号</param>
        /// <returns>诊断报告</returns>
        public DiagnosticReport RunFullDiagnostic(string clinicNo)
        {
            var report = new DiagnosticReport
            {
                ClinicNo = clinicNo,
                DiagnosticTime = DateTime.Now,
                Issues = new List<DiagnosticIssue>()
            };

            try
            {
                WriteLog($"开始诊断 - 诊疗号: {clinicNo}");

                // 1. 检查主键约束状态
                CheckPrimaryKeyConstraintStatus(report);

                // 2. 检查重复记录
                CheckDuplicateRecords(report, clinicNo);

                // 3. 检查序列号状态
                CheckSequenceStatus(report);

                // 4. 检查ORDER_NO分布
                CheckOrderNoDistribution(report, clinicNo);

                // 5. 检查最近的错误日志
                CheckRecentErrorLogs(report);

                WriteLog($"诊断完成 - 发现问题数: {report.Issues.Count}");
            }
            catch (Exception ex)
            {
                report.Issues.Add(new DiagnosticIssue
                {
                    Level = "ERROR",
                    Category = "DIAGNOSTIC",
                    Description = $"诊断过程异常: {ex.Message}",
                    Suggestion = "请检查数据库连接和权限"
                });
                WriteLog($"诊断异常: {ex.Message}");
            }

            return report;
        }

        /// <summary>
        /// 检查主键约束状态
        /// </summary>
        private void CheckPrimaryKeyConstraintStatus(DiagnosticReport report)
        {
            try
            {
                string sql = @"SELECT CONSTRAINT_NAME, STATUS, VALIDATED 
                              FROM USER_CONSTRAINTS 
                              WHERE CONSTRAINT_NAME = 'PK_OUTP_ORDERS_STANDARD1'";

                DataTable dt = db.QueryList(sql);
                if (dt.Rows.Count == 0)
                {
                    report.Issues.Add(new DiagnosticIssue
                    {
                        Level = "ERROR",
                        Category = "CONSTRAINT",
                        Description = "主键约束PK_OUTP_ORDERS_STANDARD1不存在",
                        Suggestion = "请联系数据库管理员检查表结构"
                    });
                }
                else
                {
                    var row = dt.Rows[0];
                    string status = row["STATUS"].ToString();
                    string validated = row["VALIDATED"].ToString();

                    if (status != "ENABLED")
                    {
                        report.Issues.Add(new DiagnosticIssue
                        {
                            Level = "ERROR",
                            Category = "CONSTRAINT",
                            Description = $"主键约束状态异常: {status}",
                            Suggestion = "请联系数据库管理员启用主键约束"
                        });
                    }

                    WriteLog($"主键约束状态: {status}, 验证状态: {validated}");
                }
            }
            catch (Exception ex)
            {
                report.Issues.Add(new DiagnosticIssue
                {
                    Level = "WARN",
                    Category = "CONSTRAINT",
                    Description = $"检查主键约束状态失败: {ex.Message}",
                    Suggestion = "请检查数据库权限"
                });
            }
        }

        /// <summary>
        /// 检查重复记录
        /// </summary>
        private void CheckDuplicateRecords(DiagnosticReport report, string clinicNo)
        {
            try
            {
                string sql = @"SELECT CLINIC_NO, ORDER_NO, ORDER_SUB_NO, COUNT(*) as DUPLICATE_COUNT
                              FROM OUTPDOCT.OUTP_ORDERS_STANDARD 
                              WHERE CLINIC_NO = :clinic_no
                              GROUP BY CLINIC_NO, ORDER_NO, ORDER_SUB_NO
                              HAVING COUNT(*) > 1";

                List<DbParameter> parameters = new List<DbParameter>
                {
                    db.CreateDbParameter(":clinic_no", clinicNo)
                };

                DataTable dt = db.QueryList(sql, parameters);
                if (dt.Rows.Count > 0)
                {
                    foreach (DataRow row in dt.Rows)
                    {
                        report.Issues.Add(new DiagnosticIssue
                        {
                            Level = "ERROR",
                            Category = "DUPLICATE",
                            Description = $"发现重复记录: ORDER_NO={row["ORDER_NO"]}, ORDER_SUB_NO={row["ORDER_SUB_NO"]}, 重复数量={row["DUPLICATE_COUNT"]}",
                            Suggestion = "需要清理重复数据或重新分配ORDER_NO"
                        });
                    }
                    WriteLog($"发现 {dt.Rows.Count} 组重复记录");
                }
                else
                {
                    WriteLog("未发现重复记录");
                }
            }
            catch (Exception ex)
            {
                report.Issues.Add(new DiagnosticIssue
                {
                    Level = "WARN",
                    Category = "DUPLICATE",
                    Description = $"检查重复记录失败: {ex.Message}",
                    Suggestion = "请检查数据库连接"
                });
            }
        }

        /// <summary>
        /// 检查序列号状态
        /// </summary>
        private void CheckSequenceStatus(DiagnosticReport report)
        {
            try
            {
                string sql = "SELECT OUTP_ORDER_SERIAL_NO.NEXTVAL FROM DUAL";
                object nextVal = db.GetSingleValue(sql);
                
                if (nextVal != null)
                {
                    WriteLog($"序列号OUTP_ORDER_SERIAL_NO当前值: {nextVal}");
                }
                else
                {
                    report.Issues.Add(new DiagnosticIssue
                    {
                        Level = "ERROR",
                        Category = "SEQUENCE",
                        Description = "无法获取序列号OUTP_ORDER_SERIAL_NO",
                        Suggestion = "请检查序列号是否存在"
                    });
                }
            }
            catch (Exception ex)
            {
                report.Issues.Add(new DiagnosticIssue
                {
                    Level = "ERROR",
                    Category = "SEQUENCE",
                    Description = $"检查序列号失败: {ex.Message}",
                    Suggestion = "请联系数据库管理员检查序列号"
                });
            }
        }

        /// <summary>
        /// 检查ORDER_NO分布情况
        /// </summary>
        private void CheckOrderNoDistribution(DiagnosticReport report, string clinicNo)
        {
            try
            {
                string sql = @"SELECT MIN(ORDER_NO) as MIN_ORDER_NO, 
                                     MAX(ORDER_NO) as MAX_ORDER_NO, 
                                     COUNT(*) as TOTAL_COUNT,
                                     COUNT(DISTINCT ORDER_NO) as DISTINCT_ORDER_NO_COUNT
                              FROM OUTPDOCT.OUTP_ORDERS_STANDARD 
                              WHERE CLINIC_NO = :clinic_no";

                List<DbParameter> parameters = new List<DbParameter>
                {
                    db.CreateDbParameter(":clinic_no", clinicNo)
                };

                DataTable dt = db.QueryList(sql, parameters);
                if (dt.Rows.Count > 0)
                {
                    var row = dt.Rows[0];
                    int minOrderNo = Convert.ToInt32(row["MIN_ORDER_NO"] ?? 0);
                    int maxOrderNo = Convert.ToInt32(row["MAX_ORDER_NO"] ?? 0);
                    int totalCount = Convert.ToInt32(row["TOTAL_COUNT"] ?? 0);
                    int distinctCount = Convert.ToInt32(row["DISTINCT_ORDER_NO_COUNT"] ?? 0);

                    WriteLog($"ORDER_NO分布 - 最小值: {minOrderNo}, 最大值: {maxOrderNo}, 总记录数: {totalCount}, 不同ORDER_NO数量: {distinctCount}");

                    // 检查是否有异常的ORDER_NO分布
                    if (maxOrderNo - minOrderNo + 1 != distinctCount && distinctCount > 0)
                    {
                        report.Issues.Add(new DiagnosticIssue
                        {
                            Level = "WARN",
                            Category = "DISTRIBUTION",
                            Description = $"ORDER_NO分布不连续，可能存在跳号或重复",
                            Suggestion = "建议检查ORDER_NO分配逻辑"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                report.Issues.Add(new DiagnosticIssue
                {
                    Level = "WARN",
                    Category = "DISTRIBUTION",
                    Description = $"检查ORDER_NO分布失败: {ex.Message}",
                    Suggestion = "请检查数据库连接"
                });
            }
        }

        /// <summary>
        /// 检查最近的错误日志
        /// </summary>
        private void CheckRecentErrorLogs(DiagnosticReport report)
        {
            try
            {
                string logPattern = "OrderDebug_*.log";
                var logFiles = Directory.GetFiles(LogDirectory, logPattern)
                    .Where(f => File.GetLastWriteTime(f) > DateTime.Now.AddDays(-7))
                    .OrderByDescending(f => File.GetLastWriteTime(f))
                    .Take(5);

                int errorCount = 0;
                foreach (string logFile in logFiles)
                {
                    string[] lines = File.ReadAllLines(logFile);
                    var errorLines = lines.Where(line => line.Contains("ERROR") || line.Contains("ORA-00001")).ToList();
                    errorCount += errorLines.Count;
                }

                if (errorCount > 0)
                {
                    report.Issues.Add(new DiagnosticIssue
                    {
                        Level = "WARN",
                        Category = "LOG",
                        Description = $"最近7天内发现 {errorCount} 条错误日志",
                        Suggestion = "建议查看详细日志文件分析错误原因"
                    });
                }

                WriteLog($"检查日志文件完成，发现错误数: {errorCount}");
            }
            catch (Exception ex)
            {
                WriteLog($"检查日志文件异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 写入诊断日志
        /// </summary>
        private void WriteLog(string message)
        {
            try
            {
                lock (lockObj)
                {
                    string logFile = Path.Combine(LogDirectory, $"PrimaryKeyDiagnostic_{DateTime.Now:yyyyMMdd}.log");
                    string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}{Environment.NewLine}";
                    File.AppendAllText(logFile, logEntry, Encoding.UTF8);
                }
            }
            catch
            {
                // 忽略日志写入错误
            }
        }
    }

    /// <summary>
    /// 诊断报告
    /// </summary>
    public class DiagnosticReport
    {
        public string ClinicNo { get; set; }
        public DateTime DiagnosticTime { get; set; }
        public List<DiagnosticIssue> Issues { get; set; }

        public string GenerateReport()
        {
            var sb = new StringBuilder();
            sb.AppendLine($"=== 主键冲突诊断报告 ===");
            sb.AppendLine($"诊疗号: {ClinicNo}");
            sb.AppendLine($"诊断时间: {DiagnosticTime:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"发现问题数: {Issues.Count}");
            sb.AppendLine();

            if (Issues.Count == 0)
            {
                sb.AppendLine("✅ 未发现问题");
            }
            else
            {
                foreach (var issue in Issues.OrderBy(i => i.Level))
                {
                    sb.AppendLine($"[{issue.Level}] {issue.Category}: {issue.Description}");
                    sb.AppendLine($"   建议: {issue.Suggestion}");
                    sb.AppendLine();
                }
            }

            return sb.ToString();
        }
    }

    /// <summary>
    /// 诊断问题
    /// </summary>
    public class DiagnosticIssue
    {
        public string Level { get; set; }  // ERROR, WARN, INFO
        public string Category { get; set; }  // CONSTRAINT, DUPLICATE, SEQUENCE, etc.
        public string Description { get; set; }
        public string Suggestion { get; set; }
    }
}
